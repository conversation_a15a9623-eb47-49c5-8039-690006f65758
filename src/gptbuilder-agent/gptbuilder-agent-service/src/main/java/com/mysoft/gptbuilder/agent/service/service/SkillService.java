package com.mysoft.gptbuilder.agent.service.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mysoft.framework.common.exception.BusinessLogicException;
import com.mysoft.framework.common.util.JsonUtil;
import com.mysoft.framework.dataprovider.mybatis.mapper.MyParamValueMapper;
import com.mysoft.framework.sdk.rmi.AiAssistInputClient;
import com.mysoft.framework.sdk.rmi.dto.*;
import com.mysoft.framework.sdk.system.BizaParam.BizParamService;
import com.mysoft.gptbuilder.agent.interfaces.ISkillService;
import com.mysoft.gptbuilder.agent.model.dto.*;
import com.mysoft.gptbuilder.agent.model.dto.skill.*;
import com.mysoft.gptbuilder.agent.service.dao.*;
import com.mysoft.gptbuilder.agent.service.dao.approval.PlanDao;
import com.mysoft.gptbuilder.agent.service.dao.approval.PlanDatasourceDao;
import com.mysoft.gptbuilder.agent.service.dao.entity.*;
import com.mysoft.gptbuilder.agent.service.dao.entity.approval.PlanDatasourceEntity;
import com.mysoft.gptbuilder.agent.service.dao.entity.approval.PlanEntity;
import com.mysoft.gptbuilder.agent.service.dao.entity.approval.PlanParamsEntity;
import com.mysoft.gptbuilder.agent.service.dao.entity.eval.EvalTaskEntity;
import com.mysoft.gptbuilder.agent.service.dao.eval.EvalTaskDao;
import com.mysoft.gptbuilder.agent.service.service.skill.process.NodeConfigProcessor;
import com.mysoft.gptbuilder.agent.service.service.skill.upgrade.SKillUpgradeExecutor;
import com.mysoft.gptbuilder.common.model.consts.GPTEngineAddress;
import com.mysoft.gptbuilder.common.model.consts.SkillModeConst;
import com.mysoft.gptbuilder.common.model.dto.ActionResultDTO;
import com.mysoft.gptbuilder.common.service.AppServiceBase;
import com.mysoft.gptbuilder.common.util.BusinessControlHelper;
import com.mysoft.gptbuilder.common.util.StringUtil;
import com.mysoft.sdk.aop.annotations.DisableAOP;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static co.elastic.clients.elasticsearch._types.query_dsl.Query.Kind.Wrapper;


@Slf4j
@Service
@DisableAOP
public class SkillService extends AppServiceBase implements ISkillService {

    private static final String YUN_KE_FU_APP_KEY = "gptbuilder_yunkefu_appkey";

    private static final String YUN_KE_FU_APP_SECRET = "gptbuilder_yunkefu_appsecret";

    @Autowired
    private SkillDao skillDao;

    @Autowired
    private SkillNodeDao skillNodeDao;

    @Autowired
    private SkillOrchestrationDao skillOrchestrationDao;

    @Autowired
    private PublishedSkillDao publishedSkillDao;

    @Autowired
    private SkillNodeMetadataDao skillNodeMetadataDao;

    @Autowired
    private SkillVersionsDao skillVersionsDao;

    @Autowired
    private SkillQuestionsDao skillQuestionsDao;

    @Autowired
    private SkillUserExampleDao skillUserExampleDao;

    @Autowired
    private WorkSpaceService workSpaceService;

    @Autowired
    MyParamValueMapper myParamValueMapper;

    @Autowired
    AssistantSkillDao assistantSkillDao;

    @Autowired
    PluginDao pluginDao;

    @Autowired
    KnowledgeDao knowledgeDao;

    @Autowired
    PromptDao promptDao;

    @Autowired
    ModelDao modelDao;

    @Autowired
    ModelInstanceDao modelInstanceDao;

    @Autowired
    private AiAssistInputClient aiAssistInputClient;

    @Autowired
    PlanDatasourceDao planDatasourceDao;

    @Autowired
    PlanDao planDao;

    @Autowired
    WorkSpaceDao workSpaceDao;

    @Autowired
    SkillResourceDao skillResourceDao;

    @Autowired
    private EvalTaskDao evalTaskDao;

    /**
     * 基础保存和更新技能数据的方法。
     * 该方法首先会调用{@code saveAndUpdateSkillHead}来保存或更新技能的头部信息。
     *
     * @param requestDto 技能请求数据传输对象，包含了需要保存或更新的技能信息。
     * @return 总是返回true，表示操作成功。
     */
    @Override
    public boolean baseSaveAndUpdate(SkillRequestDto requestDto) {
        // 保存或更新技能的头部信息
        saveAndUpdateSkillHead(requestDto);
        return true;
    }

    /**
     * 保存或更新技能信息。
     *
     * @param requestDto 技能请求数据传输对象，包含技能的详细信息。
     * @return 返回一个布尔值，如果技能已存在，则返回true；如果技能不存在，即新增技能，则返回false。
     * 此方法根据传入的技能请求数据传输对象（requestDto），来保存或更新数据库中的技能信息。
     * 首先，它会尝试根据requestDto中的ID查询现有的技能实体（SkillEntity）。
     * 如果找到了相应的技能实体，则更新其信息；如果没有找到，则创建一个新的技能实体并插入到数据库中。
     * 特别地，如果更新场景下存在旧的编排数据，且新请求中没有提供flow数据，则会使用旧的flow数据，以兼容某些修改场景。
     * 最后，方法返回一个布尔值，指示是更新了已存在的技能（返回true），还是新增了技能（返回false）。
     */
    public boolean saveAndUpdateSkillHead(SkillRequestDto requestDto) {
        // 根据请求DTO中的ID查询技能实体
        SkillEntity skillEntity = skillDao.selectOne(new LambdaQueryWrapper<SkillEntity>().eq(SkillEntity::getSkillGUID, requestDto.getId()));
        // 判断技能实体是否为空，不为空表示技能存在，需要更新
        boolean isUpdate = !ObjectUtils.isEmpty(skillEntity);
        if (isUpdate && skillEntity.getIsSystem() == 1) {
            //系统技能不允许进行修改、允许新增系统级技能
            throw new BusinessLogicException("系统级技能不能修改");
        }
        //是否存在节点数据
        if (isUpdate && StringUtils.isNotEmpty(skillEntity.getMetadata())) {
            String metadata = skillEntity.getMetadata();
            SkillRequestDto oldSkillDto = JsonUtil.parse(metadata, SkillRequestDto.class);
            //如果新实体里面的 flow 为空  则用上面老的覆盖 -- 兼容修改场景下存在编排数据但是保存类型为 1 的场景
            if (ObjectUtils.isEmpty(requestDto.getFlow())) {
                requestDto.setFlow(oldSkillDto.getFlow());
            }
            if (ObjectUtils.isEmpty(requestDto.getAgent())) {
                requestDto.setAgent(oldSkillDto.getAgent());
            }
            //判断是否存在某些类型的节点
            if (!ObjectUtils.isEmpty(requestDto.getFlow()) && !CollectionUtils.isEmpty(requestDto.getFlow().getNodes())) {
                List<SkillNodeDto> nodes = requestDto.getFlow().getNodes();
                String nodesStr = JsonUtil.toString(nodes);
                skillEntity.setHasImageAnalysisNode(nodesStr.contains("System_Keyword_CurrentImage") ? 1 : 0);
                skillEntity.setHasDocumentAnalysisNode(nodesStr.contains("System_Keyword_CurrentDocument") ? 1 : 0);
            }
        }
        try {
            if (isUpdate) {
                if (StringUtils.isEmpty(requestDto.getMetaDataVersion())) {
                    skillEntity.setMetaDataVersion(SKillUpgradeExecutor.latestVersion());
                }
                // 设置默认的modelInstanceCode（Agent模式下）
                if (SkillModeConst.AGENT.equals(requestDto.getMode()) &&
                    requestDto.getAgent() != null &&
                    StringUtils.isEmpty(requestDto.getAgent().getModelInstanceCode())) {
                    requestDto.getAgent().setModelInstanceCode("default_text_generation");
                }

                // 更新技能实体的各项属性
                skillEntity.setSkillCode(requestDto.getCode());
                skillEntity.setSkillName(requestDto.getName());
                skillEntity.setDescribe(requestDto.getDescription());
                skillEntity.setMode(requestDto.getMode());
                skillEntity.setModelInstanceCode(requestDto.getModelInstanceCode());
                String requestMetadata = JsonUtil.toString(requestDto);
                skillEntity.setMetadata(requestMetadata);
                skillEntity.setGuide(requestDto.getGuide());
                skillEntity.setOpenDialogWindow(requestDto.getOpenDialogWindow());
                skillEntity.setIcon(requestDto.getIcon());
                skillEntity.setInitEvent(requestDto.getInitEvent());
                skillEntity.setWelcome(requestDto.getWelcome());
                skillEntity.setSkillCategoryGUIDs(requestDto.getSkillCategoryGUIDs());
                // 查询已发布的技能实体，用于处理版本号
                List<PublishedSkillEntity> publishedSkillEntitys = publishedSkillDao.selectList(
                        new LambdaQueryWrapper<PublishedSkillEntity>().eq(PublishedSkillEntity::getSkillGUID, requestDto.getId()));
                // 如果未找到已发布的技能，则设置版本号为V001；否则，在现有版本号上加一
                if (CollectionUtils.isEmpty(publishedSkillEntitys)) {
                    skillEntity.setSkillVersions("V001");
                } else {
                    //获取版本号最大的一条
                    PublishedSkillEntity publishedSkillEntity = publishedSkillEntitys.stream().max(Comparator.comparing(PublishedSkillEntity::getSkillVersions)).get();
                    skillEntity.setSkillVersions(incrementVersion(publishedSkillEntity.getSkillVersions()));
                }
                if (!ObjectUtils.isEmpty(requestDto.getStartup())) {
                    if (StringUtils.isNotBlank(requestDto.getStartup().getPluginGUID())) {
                        skillEntity.setStartUpPluginGUID(requestDto.getStartup().getPluginGUID());
                    }
                    if (StringUtils.isNotBlank(requestDto.getStartup().getToolGUID())) {
                        skillEntity.setStartUpToolGUID(requestDto.getStartup().getToolGUID());
                    }
                }
                // 保存更新后的技能实体
                skillDao.updateById(skillEntity);
            } else {
                // 如果技能实体为空，即技能不存在，则创建新的技能实体并插入到数据库中
                skillEntity = new SkillEntity();
                skillEntity.setMetaDataVersion(SKillUpgradeExecutor.latestVersion());

                // 设置默认的modelInstanceCode（Agent模式下）
                if (SkillModeConst.AGENT.equals(requestDto.getMode()) &&
                    requestDto.getAgent() != null &&
                    StringUtils.isEmpty(requestDto.getAgent().getModelInstanceCode())) {
                    requestDto.getAgent().setModelInstanceCode("default_text_generation");
                }

                // 设置新技能实体的各项属性
                skillEntity.setSkillGUID(UUID.randomUUID().toString());
                skillEntity.setSkillCode(requestDto.getCode());
                skillEntity.setSkillName(requestDto.getName());
                skillEntity.setDescribe(requestDto.getDescription());
                skillEntity.setMode(requestDto.getMode());
                skillEntity.setGuide(requestDto.getGuide());
                skillEntity.setModelInstanceCode(requestDto.getModelInstanceCode());
                skillEntity.setOpenDialogWindow(requestDto.getOpenDialogWindow());
                skillEntity.setIcon(requestDto.getIcon());
                skillEntity.setSpaceGUID(requestDto.getSpaceGUID());
                skillEntity.setSkillCategoryGUIDs(requestDto.getSkillCategoryGUIDs());
                // 新增的技能默认设置为非系统技能
                skillEntity.setIsSystem(0);
                skillEntity.setSkillVersions("V001");
                skillEntity.setInitEvent(requestDto.getInitEvent());
                requestDto.setId(skillEntity.getSkillGUID());
                if (!ObjectUtils.isEmpty(requestDto.getStartup())) {
                    if (StringUtils.isNotBlank(requestDto.getStartup().getPluginGUID())) {
                        skillEntity.setStartUpPluginGUID(requestDto.getStartup().getPluginGUID());
                    }
                    if (StringUtils.isNotBlank(requestDto.getStartup().getToolGUID())) {
                        skillEntity.setStartUpToolGUID(requestDto.getStartup().getToolGUID());
                    }
                }
                String requestMetadata = JsonUtil.toString(requestDto);
                skillEntity.setMetadata(requestMetadata);
                // 插入新的技能实体
                skillDao.insert(skillEntity);
                // 更新请求DTO中的ID为新插入的技能GUID
                requestDto.setId(skillEntity.getSkillGUID());
            }
        } catch (Exception e) {
            // 抛出业务逻辑异常，表示技能基础数据保存失败
            throw new BusinessLogicException("技能基础数据保存失败");
        }
        // 返回技能是否已存在的标识
        return isUpdate;
    }


    /**
     * 保存或更新技能请求数据。
     *
     * @param requestDto 技能请求数据传输对象，包含要保存或更新的技能信息。
     * @return 如果技能是更新模式则返回true，否则返回false。特定的保存模式由requestDto中的saveMode字段决定。
     * @throws BusinessLogicException 如果技能请求对象为空，则抛出此异常。
     */
    @Override
    public String saveAndUpdate(SkillRequestDto requestDto) {
        if (ObjectUtils.isEmpty(requestDto)) {
            throw new BusinessLogicException("技能请求对象为空");
        }
        SkillEntity skillEntity = skillDao.selectOne(new LambdaQueryWrapper<SkillEntity>().eq(SkillEntity::getSkillGUID, requestDto.getId()));
        // 判断技能实体是否为空，不为空表示技能存在，需要更新
        boolean isUpdate = !ObjectUtils.isEmpty(skillEntity);
        if(!isUpdate && !BusinessControlHelper.IsStandAlone()){
            throw new BusinessLogicException("保存失败：当前版本不允许新增技能，请联系系统管理员。");
        }
        boolean isUpgrade = isUpdate && SKillUpgradeExecutor.isNeedUpgrade(requestDto.getMetaDataVersion());
        //元数据版本升级
        if (isUpgrade) {
            SKillUpgradeExecutor executor = new SKillUpgradeExecutor(requestDto);
            requestDto = executor.upgrade();
        } else {
            // 校验请求数据
            saveValidate(requestDto);
        }
        // 判断是修改还是新增保存场景，赋值 true 代表修改
        saveAndUpdateSkillHead(requestDto);
        // 保存预置问题
        saveSkillQuestion(requestDto);
        saveSkillUserExample(requestDto);
        if (requestDto.getSaveMode() == 1) {
            return requestDto.getId();
        }
        /**
         * 技能节点数据处理：
         * 1、保存 node 数据
         * 2、保存 relse 的 数据
         */
        handleSkillNode(requestDto, isUpdate, isUpgrade);
        if (requestDto.getSaveMode() == 2) {
            return requestDto.getId();
        }
        handleSkillPublishing(requestDto);
        return requestDto.getId();
    }

    private void handleSkillPublishing(SkillRequestDto requestDto) {
        if (SkillModeConst.FLOW.equals(requestDto.getMode())) {
            // 编排数据解析生成
            List<SkillOrchestrationContextDto> skillOrchestrationContextDto = skillOrchestrationSave(requestDto);
            // 发布技能
            publishSkill(requestDto, skillOrchestrationContextDto);
        } else {
            publishSkill(requestDto, null);
        }
    }

    private void handleSkillNode(SkillRequestDto requestDto, boolean isUpdate, boolean isUpgrade) {
        if (SkillModeConst.FLOW.equals(requestDto.getMode())) {
            //如果节点的数据未空  不处理节点数据（前提不存在节点未空的技能）
            if (ObjectUtils.isEmpty(requestDto.getFlow()) && CollectionUtils.isEmpty(requestDto.getFlow().getNodes())) {
                return;
            }
            //删除技能子表数据
            deleteSkillNodeDto(requestDto, isUpdate);
            // 节点数据保存
            skillNodeSave(requestDto, isUpdate, isUpgrade);
        } else if (SkillModeConst.AGENT.equals(requestDto.getMode())) {
            //todo：保存节点数据伪造开始结束节点的数据
            deleteSkillNodeDto(requestDto, isUpdate);
            skillAgentNodeSave(requestDto, isUpdate, isUpgrade);
            //todo：创建关联数据
            saveAgentResource(requestDto, isUpdate);
        }
    }

    private void saveAgentResource(SkillRequestDto requestDto, boolean isUpdate) {
        // 判断是否为更新操作
        if (isUpdate) {
            // 如果是更新操作，先删除原有节点数据
            skillNodeDelete(requestDto.getId());
            // 再进行新增操作
        }
        skillResourceDao.delete(new LambdaQueryWrapper<SkillResourceEntity>().eq(SkillResourceEntity::getSkillGUID, requestDto.getId()));
        SkillRequestDto.AgentDTO agent = requestDto.getAgent();

        // 设置默认的modelInstanceCode
        if (StringUtils.isEmpty(agent.getModelInstanceCode())) {
            agent.setModelInstanceCode("default_text_generation");
        }

        if (!CollectionUtils.isEmpty(agent.getKnowledgs())) {
            agent.getKnowledgs().forEach(knowledges -> {
                saveSkillResource(requestDto.getId(), knowledges.getId(), knowledges.getCode(), "Knowledge", null);
            });
        }
        if (!CollectionUtils.isEmpty(agent.getTools())) {
            agent.getTools().forEach(tools -> {
               saveSkillResource(requestDto.getId(), tools.getPluginGUID(), tools.getPath(), "Plugin", null);
            });
        }
        if (!CollectionUtils.isEmpty(agent.getMcps())) {
            agent.getMcps().forEach(mcp -> {
                // 根据是否有 toolGUID 和 toolName 来判断资源类型
                if (StringUtils.isNotEmpty(mcp.getToolGUID()) && StringUtils.isNotEmpty(mcp.getToolName())) {
                    // 如果有 toolGUID 和 toolName，则 ResourceType 为 McpTool，ResourceCode 为 toolName
                    saveSkillResource(requestDto.getId(), mcp.getToolGUID(), mcp.getToolName(), "McpTool", null);
                } else if (StringUtils.isNotEmpty(mcp.getServiceGUID())) {
                    // 如果只有 serviceGUID，则 ResourceType 为 Mcp，ResourceCode 为 serviceCode
                    saveSkillResource(requestDto.getId(), mcp.getServiceGUID(), mcp.getServiceCode(), "Mcp", null);
                }
            });
        }
    }


    //添加创建保存SkillResourceEntity的方法
    private void saveSkillResource(String skillGUID, String resourceGUID, String resourceCode, String typr, String nodeId) {
        //创建SkillResourceEntity
        SkillResourceEntity skillResourceEntity = new SkillResourceEntity();
        skillResourceEntity.setSkillGUID(skillGUID);
        skillResourceEntity.setResourceGUID(resourceGUID);
        skillResourceEntity.setResourceCode(resourceCode);
        skillResourceEntity.setResourceType(typr);
        skillResourceEntity.setNodeGUID(nodeId);
        skillResourceDao.insert(skillResourceEntity);
    }

    private void skillAgentNodeSave(SkillRequestDto requestDto, boolean isUpdate, boolean isUpgrade) {
        // 判断是否为更新操作
        if (isUpdate) {
            // 如果是更新操作，先删除原有节点数据
            skillNodeDelete(requestDto.getId());
            // 再进行新增操作
        }
        if (!SkillModeConst.AGENT.equals(requestDto.getMode())) {
            return;
        }
        //创建开始结束节点
        List<SkillNodeEntity> agentNode = createAgentNode(requestDto.getAgent(), requestDto.getId());
        for (SkillNodeEntity skillNodeEntity : agentNode) {
           skillNodeDao.insert(skillNodeEntity);
        }
    }

    private List<SkillNodeEntity> createAgentNode(SkillRequestDto.AgentDTO agent, String skillGUID) {
        // 设置默认的modelInstanceCode
        if (StringUtils.isEmpty(agent.getModelInstanceCode())) {
            agent.setModelInstanceCode("default_text_generation");
        }

        List<SkillNodeEntity> skillNodeEntities = new ArrayList<>();
        SkillNodeEntity startNode = new SkillNodeEntity();
        startNode.setSkillGUID(skillGUID);
        startNode.setNodeGUID(UUID.randomUUID().toString()); // 为节点生成一个唯一的全局标识符。
        startNode.setNodeCode("start0"); // 设置节点代码。
        startNode.setNodeName("开始"); // 设置节点名称。
        startNode.setNodeType("Start"); // 设置节点类型。
        skillNodeEntities.add(startNode);
        SkillNodeEntity endNode = new SkillNodeEntity();
        endNode.setSkillGUID(skillGUID);
        endNode.setNodeGUID(UUID.randomUUID().toString()); // 为节点生成一个唯一的全局标识符。
        endNode.setNodeCode("end0"); // 设置节点代码。
        endNode.setNodeName("结束步骤：完成执行动作"); // 设置节点名称。
        endNode.setNodeType("End"); // 设置节点类型。
        skillNodeEntities.add(endNode);
        return skillNodeEntities;
    }

    @Override
    @Transactional
    public String saveAndUpdateNode(SkillRequestDto requestDto) {
        if (ObjectUtils.isEmpty(requestDto) || StringUtils.isEmpty(requestDto.getId())) {
            throw new BusinessLogicException("技能请求对象为空");
        }
        SkillEntity skillEntity = skillDao.selectOne(new LambdaQueryWrapper<SkillEntity>().eq(SkillEntity::getSkillGUID, requestDto.getId()));
        // 判断原技能实体是否存在
        if (ObjectUtils.isEmpty(skillEntity)) {
            throw new BusinessLogicException("不存在该技能");
        }
        if (skillEntity.getIsSystem() == 1) {
            //系统技能不允许进行修改、允许新增系统级技能
            throw new BusinessLogicException("系统级技能不允许调整");
        }
        if (StringUtils.isEmpty(requestDto.getMetaDataVersion())) {
            requestDto.setMetaDataVersion(skillEntity.getMetaDataVersion());
        }
        if (SkillModeConst.FLOW.equals(requestDto.getMode())) {
            boolean isUpgrade = SKillUpgradeExecutor.isNeedUpgrade(skillEntity.getMetaDataVersion());
            if (isUpgrade) {
                //元数据版本升级
                SKillUpgradeExecutor executor = new SKillUpgradeExecutor(requestDto);
                requestDto = executor.upgrade();
            } else {
                // 校验请求数据
                nodeValidate(requestDto);
            }
        } else if (SkillModeConst.AGENT.equals(requestDto.getMode())) {
            agentValidate(requestDto);
        }

        // 准备并初始化新的技能实体
        SkillRequestDto newSkillRequestDto = SkillRequestDto.coverToDto(skillEntity.getMetadata());
        newSkillRequestDto.setSaveMode(requestDto.getSaveMode());
        newSkillRequestDto.setFlow(requestDto.getFlow());
        newSkillRequestDto.setAgent(requestDto.getAgent());
        newSkillRequestDto.setMetaDataVersion(requestDto.getMetaDataVersion());
        if (SkillModeConst.FLOW.equals(requestDto.getMode())) {
            //判断是否存在某些类型的节点
            if (!ObjectUtils.isEmpty(requestDto.getFlow()) && !CollectionUtils.isEmpty(requestDto.getFlow().getNodes())) {
                List<SkillNodeDto> nodes = requestDto.getFlow().getNodes();
                SkillNodeDto startNode = nodes.get(0);
                Object uploadables = startNode.getConfig().get("uploadables");
                if(uploadables != null){
                    skillEntity.setUploadables(uploadables.toString());
                } else{
                    skillEntity.setUploadables("");
                }
                skillEntity.setHasInteractiveCardNode(nodes.stream().filter(f-> "Card".equals(f.getType())
                        && "interactive".equals(f.getConfig().get("type"))).findAny().isPresent() ? 1 : 0);
                String nodesStr = JsonUtil.toString(nodes);
                skillEntity.setHasImageAnalysisNode(nodesStr.contains("System_Keyword_CurrentImage") ? 1 : 0);
                skillEntity.setHasDocumentAnalysisNode(nodesStr.contains("System_Keyword_CurrentDocument") ? 1 : 0);
            }
        }
        //更新技能基础表元数据
        skillEntity.setMetadata(JsonUtil.toString(newSkillRequestDto));
        if (StringUtils.isEmpty(skillEntity.getMetaDataVersion())) {
            skillEntity.setMetaDataVersion(SKillUpgradeExecutor.latestVersion());
        } else {
            skillEntity.setMetaDataVersion(newSkillRequestDto.getMetaDataVersion());
        }

        skillDao.updateById(skillEntity);
        // 判断是修改还是新增保存场景，赋值 true 代表修改
        List<SkillNodeEntity> skillNodeEntitys = skillNodeDao.selectList(new LambdaQueryWrapper<SkillNodeEntity>().eq(SkillNodeEntity::getSkillGUID, newSkillRequestDto.getId()));
        // 判断技能实体是否为空，不为空表示技能存在，需要更新
        boolean isUpdate = !CollectionUtils.isEmpty(skillNodeEntitys);

        /**
         * 技能节点数据处理：
         * 1、保存 node 数据
         * 2、保存 relse 的 数据
         */
        handleSkillNode(newSkillRequestDto, isUpdate, false);
        handleSkillPublishing(newSkillRequestDto);
        return newSkillRequestDto.getId();
    }

    @Autowired
    private BizParamService bizParamService;

    @Override
    public SkillStartupMipResponse getSkillStartupConfig(HttpServletRequest request, AssistantChatDto assistantChatDto) {


        if (!StringUtils.isNotEmpty(assistantChatDto.getSkillGUID())) {
            return null;
        }
        SkillEntity skillEntity = skillDao.selectOne(new LambdaQueryWrapper<SkillEntity>().eq(SkillEntity::getSkillGUID, assistantChatDto.getSkillGUID()));
        if (ObjectUtils.isEmpty(skillEntity)) {
            return null;
        }
        //构建请求参数
        SkillStartupMipRequest skillStartupMipRequest = new SkillStartupMipRequest();
        com.mysoft.framework.dataprovider.mybatis.entity.MyParamValue appKey =
                myParamValueMapper.selectOne(new LambdaQueryWrapper<com.mysoft.framework.dataprovider.mybatis.entity.MyParamValue>().eq(com.mysoft.framework.dataprovider.mybatis.entity.MyParamValue::getParamCode,
                        YUN_KE_FU_APP_KEY));
        com.mysoft.framework.dataprovider.mybatis.entity.MyParamValue appSecret =
                myParamValueMapper.selectOne(new LambdaQueryWrapper<com.mysoft.framework.dataprovider.mybatis.entity.MyParamValue>().eq(com.mysoft.framework.dataprovider.mybatis.entity.MyParamValue::getParamCode,
                        YUN_KE_FU_APP_SECRET));
        skillStartupMipRequest.setAppKey(appKey.getValue());
        skillStartupMipRequest.setAppSecret(appSecret.getValue());
        if (!CollectionUtils.isEmpty(assistantChatDto.getArguments())) {
            List<AssistantChatDto.ArgumentsDTO> arguments = assistantChatDto.getArguments();
            skillStartupMipRequest.setInput(assistantChatDto.getInput());
            String appCode = arguments.stream()
                    .filter(item -> SystemParamConst.SYSTEM_CODE.getCode().equals(item.getKey()))
                    .map(AssistantChatDto.ArgumentsDTO::getValue)
                    .findFirst()
                    .orElse("");
            skillStartupMipRequest.setAppCode(appCode);
            String appName = arguments.stream()
                    .filter(item -> SystemParamConst.SYSTEM_NAME.getCode().equals(item.getKey()))
                    .map(AssistantChatDto.ArgumentsDTO::getValue)
                    .findFirst()
                    .orElse("");
            skillStartupMipRequest.setAppName(appName);
            String appVersion = arguments.stream()
                    .filter(item -> SystemParamConst.APP_VERSION.getCode().equals(item.getKey()))
                    .map(AssistantChatDto.ArgumentsDTO::getValue)
                    .findFirst()
                    .orElse("");
            skillStartupMipRequest.setAppVersion(appVersion);
            String erpVersion = arguments.stream()
                    .filter(item -> SystemParamConst.ERP_VERSION.getCode().equals(item.getKey()))
                    .map(AssistantChatDto.ArgumentsDTO::getValue)
                    .findFirst()
                    .orElse("");
            skillStartupMipRequest.setErpVersion(erpVersion);
            String mobile = arguments.stream()
                    .filter(item -> SystemParamConst.USER_MOBILE.getCode().equals(item.getKey()))
                    .map(AssistantChatDto.ArgumentsDTO::getValue)
                    .findFirst()
                    .orElse("");
            skillStartupMipRequest.setMobile(mobile);
            String userName = arguments.stream()
                    .filter(item -> SystemParamConst.User_Name.getCode().equals(item.getKey()))
                    .map(AssistantChatDto.ArgumentsDTO::getValue)
                    .findFirst()
                    .orElse("");
            skillStartupMipRequest.setUserName(userName);
            String customerId = arguments.stream()
                    .filter(item -> SystemParamConst.CUSTOMER_GUID.getCode().equals(item.getKey()))
                    .map(AssistantChatDto.ArgumentsDTO::getValue)
                    .findFirst()
                    .orElse("");
            skillStartupMipRequest.setCustomerId(customerId);
            String customerName = arguments.stream()
                    .filter(item -> SystemParamConst.CUSTOMER_NAME.getCode().equals(item.getKey()))
                    .map(AssistantChatDto.ArgumentsDTO::getValue)
                    .findFirst()
                    .orElse("");
            skillStartupMipRequest.setCustomerName(customerName);
        }
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("uri", skillEntity.getStartUpToolGUID());
        requestBody.put("jsonBody", JsonUtil.toString(skillStartupMipRequest));
        try {
            ActionResultDTO actionResultDTO = this.callEngine(GPTEngineAddress.PLUGIN_MIP, requestBody, this.getMysoftContext(request.getCookies()));
            System.out.println(JsonUtil.toString(actionResultDTO));

            SkillStartupMipResponse parse = JsonUtil.parse((String) actionResultDTO.getData(), SkillStartupMipResponse.class);
            return parse;
        } catch (Exception ex) {
            log.error("#### 参数对比错误 = {}", ex.getMessage());
        }
        return null;
    }

    @Override
    public void updateSpaceGUID(String businessGUID, String newSpaceGUID) {
        SkillEntity skillEntity = new SkillEntity();
        skillEntity.setSpaceGUID(newSpaceGUID);
        skillEntity.setSkillGUID(businessGUID);
        skillDao.updateById(skillEntity);
    }

    @Override
    public String queryInitEvent(String skillGUID) {
        SkillEntity skillEntity = skillDao.selectById(skillGUID);
        return skillEntity.getInitEvent();
    }

    @Override
    public boolean engineUpgradeSkill(String skillGUID) {
        // 校验输入参数不能为空
        if (StringUtils.isEmpty(skillGUID)) {
            throw new BusinessLogicException("技能GUID不能为空");
        }
        // 查询原技能实体
        SkillEntity skillEntity = skillDao.selectOne(new LambdaQueryWrapper<SkillEntity>().eq(SkillEntity::getSkillGUID, skillGUID));
        if (ObjectUtils.isEmpty(skillEntity)) {
            throw new BusinessLogicException("技能不存在");
        }
        if (SKillUpgradeExecutor.isNeedUpgrade(skillEntity.getMetaDataVersion())) {
            // 准备并初始化新的技能实体
            SkillRequestDto skillRequestDto = SkillRequestDto.coverToDto(skillEntity.getMetadata());
            saveAndUpdateNode(skillRequestDto);
            return true;
        }
        return false;

    }

    @Override
    public SkillAllDto querySkillAllByGUID(String skillGUID) {
        // 校验输入参数不能为空
        if (StringUtils.isEmpty(skillGUID)) {
            throw new BusinessLogicException("技能GUID不能为空");
        }
        // 查询原技能实体
        SkillEntity skillEntity = skillDao.selectOne(new LambdaQueryWrapper<SkillEntity>().eq(SkillEntity::getSkillGUID, skillGUID));
        if (ObjectUtils.isEmpty(skillEntity)) {
            throw new BusinessLogicException("技能不存在");
        }
        SkillAllDto skillAllDto = BeanUtil.copyProperties(skillEntity, SkillAllDto.class);
        SkillRequestDto skillRequestDto = SkillRequestDto.coverToDto(skillEntity.getMetadata());
        skillAllDto.setSkillRequestDto(skillRequestDto);
        // 设置技能实体的节点信息
        SkillRequestDto requestDto = SkillRequestDto.coverToDto(skillEntity.getMetadata());
        if (requestDto.getMode().equalsIgnoreCase("flow")) {
            List<SkillNodeDto> nodes = requestDto.getFlow().getNodes();
            skillAllDto.setMaxUploadNumber(1);
            if ("Start".equals(nodes.get(0).getType())) {
                SystemPluginNodeConfigDto configDto = (SystemPluginNodeConfigDto) new NodeConfigProcessor(nodes.get(0)).parseConfigDto();
                boolean hasMaxUploadNumber = nodes.get(0).getConfig().containsKey("maxUploadNumber");
                int maxUploadNumber = hasMaxUploadNumber ? configDto.getMaxUploadNumber() : 1;
                skillAllDto.setMaxUploadNumber(maxUploadNumber);
            }
        } else {
            skillAllDto.setMaxUploadNumber(requestDto.getAgent().getMaxUploadNumber());
            skillAllDto.setUploadables(requestDto.getAgent().getUploadables());
        }

        return skillAllDto;
    }

    private void saveSkillUserExample(SkillRequestDto requestDto) {
        // 每次技能的新增修改，上来先删除已存在的技能问题，在做后面的保存
        skillUserExampleDao.delete(new LambdaQueryWrapper<SkillUserExampleEntity>().eq(SkillUserExampleEntity::getSkillGUID, requestDto.getId()));

        // 如果没有问题需要保存，直接返回
        if (CollectionUtils.isEmpty(requestDto.getExamples())) {
            return;
        }

        // 遍历并保存新的技能问题
        requestDto.getExamples().stream().map(userExampleDto -> {
            SkillUserExampleEntity entity = new SkillUserExampleEntity();
            entity.setSkillGUID(requestDto.getId());
            entity.setSkillUserExampleGUID(UUID.randomUUID().toString()); // 为每个问题生成唯一的GUID
            entity.setSort(userExampleDto.getSort()); // 设置问题的排序位置
            entity.setUserQuestion(userExampleDto.getUserQuestion());
            entity.setAssistantAnswer(userExampleDto.getAssistantAnswer());
            skillUserExampleDao.insert(entity); // 保存问题到数据库
            return entity;
        }).collect(Collectors.toList());
    }

    /**
     * 保存技能问题的方法。
     * 该方法首先会删除与给定技能GUID相关的所有问题，然后根据传入的请求DTO（如果问题非空）保存新的问题。
     *
     * @param requestDto 技能请求数据传输对象，包含技能ID和问题列表。
     *                   其中，技能ID用于标识要操作的技能，问题列表则用于保存或更新技能相关的问题。
     */
    private void saveSkillQuestion(SkillRequestDto requestDto) {
        // 每次技能的新增修改，上来先删除已存在的技能问题，在做后面的保存
        skillQuestionsDao.delete(new LambdaQueryWrapper<SkillQuestionsEntity>().eq(SkillQuestionsEntity::getSkillGUID, requestDto.getId()));

        // 如果没有问题需要保存，直接返回
        if (CollectionUtils.isEmpty(requestDto.getQuestions())) {
            return;
        }

        // 遍历并保存新的技能问题
        requestDto.getQuestions().stream().map(question -> {
            SkillQuestionsEntity entity = new SkillQuestionsEntity();
            entity.setSkillGUID(requestDto.getId());
            entity.setSkillQuestionsGUID(UUID.randomUUID().toString()); // 为每个问题生成唯一的GUID
            entity.setSort(question.getSort()); // 设置问题的排序位置
            entity.setQuestion(question.getQuestion()); // 设置问题内容
            skillQuestionsDao.insert(entity); // 保存问题到数据库
            return entity;
        }).collect(Collectors.toList());
    }

    /**
     * 根据请求删除技能节点及相关数据。
     *
     * @param requestDto 技能请求数据传输对象，包含需要操作的技能的详细信息。
     * @param isUpdate   指示是否为更新操作的布尔值。如果为false，则不执行任何删除操作。
     */
    private void deleteSkillNodeDto(SkillRequestDto requestDto, Boolean isUpdate) {
        // 如果不是更新操作，则直接返回，不执行删除
        if (!isUpdate) {
            return;
        }

        String skillGUID = requestDto.getId();
        // 如果保存模式大于等于2，删除技能节点及其元数据
        if (requestDto.getSaveMode() >= 2) {
            // 删除技能节点
            skillNodeDao.delete(new LambdaQueryWrapper<SkillNodeEntity>().eq(SkillNodeEntity::getSkillGUID, skillGUID));
            // 删除技能节点元数据
            skillNodeMetadataDao.delete(new LambdaQueryWrapper<SkillNodeMetadataEntity>().eq(SkillNodeMetadataEntity::getSkillGUID, skillGUID));
            //todo:这里还需要添加工具关联数据表的数据
        }
        // 如果保存模式为3，额外删除技能编排信息和已发布的技能信息
        if (requestDto.getSaveMode() == 3) {
            // 删除技能编排信息
            skillOrchestrationDao.delete(new LambdaQueryWrapper<SkillOrchestrationEntity>().eq(SkillOrchestrationEntity::getSkillGUID, skillGUID));
            // 删除发布的技能信息
            publishedSkillDao.delete(new LambdaQueryWrapper<PublishedSkillEntity>().eq(PublishedSkillEntity::getSkillGUID, skillGUID));
        }
    }

    /**
     * 节点数据保存方法。
     * 该方法用于根据请求DTO（数据传输对象）保存或更新技能节点数据。
     * 如果isUpdate参数为true，则先删除原有的节点数据，再进行新增操作。
     *
     * @param requestDto 包含技能节点数据的请求DTO，用于新增或更新节点数据。
     * @param isUpdate   指示是否为更新操作的布尔值。true表示更新，false表示新增。
     */
    private void skillNodeSave(SkillRequestDto requestDto, Boolean isUpdate, Boolean isUpgrade) {
        // 判断是否为更新操作
        if (isUpdate) {
            // 如果是更新操作，先删除原有节点数据
            skillNodeDelete(requestDto.getId());
            // 再进行新增操作
        }
        skillNodeInsert(requestDto, isUpgrade);
    }

    /**
     * 技能节点插入处理方法。
     * 该方法将根据提供的技能请求数据传输对象（SkillRequestDto），插入一个技能节点。
     *
     * @param requestDto 技能请求数据传输对象，包含要插入的技能节点的详细信息。
     *                   其中包含了技能流程（flow）和节点ID（id）。
     *                   - flow: 技能流程，定义了技能节点所属的流程。
     *                   - id: 节点ID，唯一标识一个技能节点。
     */
    private void skillNodeInsert(SkillRequestDto requestDto, Boolean isUpgrade) {
        // 使用请求DTO中的flow和id调用另一个skillNodeInsert方法进行技能节点的插入
        skillNodeInsert(requestDto.getFlow(), requestDto.getId(), isUpgrade);
    }


    /**
     * 技能节点插入方法
     * 该方法用于处理技能编排的节点插入逻辑。它首先校验输入的技能编排数据是否为空，然后通过解析节点和边的数据，
     * 复制节点信息到数据库，并处理每个节点的元数据。此过程会为每个节点创建实体并存储到对应的数据库表中。
     *
     * @param flow      技能编排的流程数据传输对象，包含节点和边的信息。
     * @param skillGUID 技能的全局唯一标识符，用于标识技能。
     */
    private void skillNodeInsert(SkillRequestDto.FlowDTO flow, String skillGUID, Boolean isUpgrade) {
        // 校验技能编排数据是否为空
        if (ObjectUtils.isEmpty(flow)) {
            throw new BusinessLogicException("技能编排数据为空");
        }
        skillResourceDao.delete(new LambdaQueryWrapper<SkillResourceEntity>().eq(SkillResourceEntity::getSkillGUID, skillGUID));
        List<SkillNodeDto> nodes = flow.getNodes();
        List<SkillRequestDto.FlowDTO.EdgesDTO> edges = flow.getEdges();

        // 校验节点和连接线数据是否为空
        if (CollectionUtils.isEmpty(nodes) || CollectionUtils.isEmpty(edges)) {
            throw new BusinessLogicException("技能编排节点或连接线数据为空");
        }

        // 根据源节点代码将边数据分组，以便后续处理
        Map<String, SkillRequestDto.FlowDTO.EdgesDTO> edgesGroupBySource
                = edges.stream().collect(Collectors
                .toMap(SkillRequestDto.FlowDTO.EdgesDTO::getSource, Function.identity(), (v1, v2) -> v1));

        // 复制节点信息到数据库，并处理节点的元数据
        HashMap<String, SkillNodeDto> nodeWithId = new HashMap<>();
        ArrayList<SkillNodeEntity> nodeEntities = Lists.newArrayList();
        ArrayList<SkillNodeMetadataEntity> metadataEntities = Lists.newArrayList();

        nodes.forEach(node -> {
            SkillNodeEntity skillNodeEntity = createSkillNode(skillGUID, node, edgesGroupBySource.get(node.getCode()), isUpgrade);
            // 获取与当前节点相连的边信息
            // 插入节点信息到数据库
            skillNodeDao.insert(skillNodeEntity);

            // 处理节点的配置数据，并存储到元数据表中
            String nodeString = JsonUtil.toString(node);
            SkillNodeMetadataEntity metadataEntity = new SkillNodeMetadataEntity();
            metadataEntity.setSkillNodeMetadataGUID(UUID.randomUUID().toString());
            metadataEntity.setNodeGUID(skillNodeEntity.getNodeGUID());
            metadataEntity.setSkillGUID(skillGUID);
            // 保存整个Node节点数据到元数据
            metadataEntity.setMetadata(nodeString);
            // 插入节点元数据到数据库
            skillNodeMetadataDao.insert(metadataEntity);

            // 收集插入的实体，以便后续操作
            node.setId(skillNodeEntity.getNodeGUID());
            nodeEntities.add(skillNodeEntity);
            metadataEntities.add(metadataEntity);
            nodeWithId.put(node.getCode(), node);
        });
    }

    /**
     * 创建技能节点实体。
     *
     * @param skillGUID 技能的全局唯一标识符。
     * @param node      技能节点的DTO（数据传输对象），包含节点的基本信息。
     * @param edgesDTO  技能请求流程中的边DTO，用于表示当前节点到下一个节点的连接信息，可能为空。
     * @return 返回创建的技能节点实体。
     */
    private SkillNodeEntity createSkillNode(String skillGUID, SkillNodeDto node, SkillRequestDto.FlowDTO.EdgesDTO edgesDTO, Boolean isUpgrade) {
        SkillNodeEntity skillNodeEntity = new SkillNodeEntity();
        skillNodeEntity.setSkillGUID(skillGUID);
        skillNodeEntity.setNodeGUID(UUID.randomUUID().toString()); // 为节点生成一个唯一的全局标识符。
        skillNodeEntity.setNodeCode(node.getCode()); // 设置节点代码。
        skillNodeEntity.setNodeName(node.getName()); // 设置节点名称。
        skillNodeEntity.setNodeType(node.getType()); // 设置节点类型。
        // 如果存在下一个节点的信息，则保存下一个节点的代码。
//        if (!ObjectUtils.isEmpty(edgesDTO)) {
//            skillNodeEntity.setNextNodeCode(edgesDTO.getTarget());
//        }
        skillNodeEntity.setDescribe(node.getDescription()); // 设置节点描述。
        if (isUpgrade) {
            return skillNodeEntity;
        }
        // 如果节点类型为Plugin，解析并设置插件配置信息。
        if ("Plugin".equals(node.getType())) {
            PluginNodeConfigDto configDto = (PluginNodeConfigDto) new NodeConfigProcessor(node).parseConfigDto();
            if(StringUtils.isNotBlank(configDto.getPluginId())){
                PluginEntity pluginEntity = pluginDao.selectOne(Wrappers.<PluginEntity>lambdaQuery().select(PluginEntity::getPluginGUID).eq(PluginEntity::getPluginGUID, configDto.getPluginId()));
                if (pluginEntity == null){
                    throw new BusinessLogicException("插件不存在");
                }
            }
            skillNodeEntity.setPluginGUID(configDto.getPluginId());
            saveSkillResource(skillGUID, configDto.getPluginId(), null,"Plugin", node.getId());
        }
        if ("PromptTemplate".equals(node.getType())) {
            PromptTemplateNodeConfigDto configDto = (PromptTemplateNodeConfigDto) new NodeConfigProcessor(node).parseConfigDto();
            if(StringUtils.isNotBlank(configDto.getTemplateId())){
                PromptEntity promptEntity = promptDao.selectOne(Wrappers.<PromptEntity>lambdaQuery().select(PromptEntity::getPromptGUID).eq(PromptEntity::getPromptGUID, configDto.getTemplateId()));
                if (promptEntity == null) {
                    throw new BusinessLogicException("提示词不存在");
                }
            }
            if(!CollectionUtils.isEmpty(configDto.getFiles())){
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                AbstractNodeConfigDto.ParamValueDto fileInfoDto = new AbstractNodeConfigDto.ParamValueDto();
                try {
                    fileInfoDto = objectMapper.readValue(JsonUtil.toString(configDto.getFiles().get(0).getValue()), new TypeReference<AbstractNodeConfigDto.ParamValueDto>() {});
                } catch (JsonProcessingException e) {
                    throw new BusinessLogicException("节点【" + node.getName() + "】序列化错误");
                }
                if(!StringUtils.isEmpty(fileInfoDto.getContent())){
                    if (!isPromptAvailable("3",configDto.getTemplateId(),true)) {
                        throw new BusinessLogicException("节点【" + node.getName() + "】只能选择绑定了 文档服务实例的提示词或者是模型实例为空的提示词");
                    }
                    if (configDto.getMemories() > 0) {
                        throw new BusinessLogicException("节点【" + node.getName() + "】提示词模型为文档模型服务时不支持会话记忆");
                    }
                }
                if(StringUtils.isEmpty(fileInfoDto.getContent()) && isPromptAvailable("3",configDto.getTemplateId(),false)) {
                    throw new BusinessLogicException("节点【" + node.getName() + "】分析文档为空，不支持选择文档模型服务");
                }
            }
            skillNodeEntity.setPromptGUID(configDto.getTemplateId());
            saveSkillResource(skillGUID, configDto.getTemplateId(), null, "PromptTemplate", node.getId());
        }
        if ("ImageAnalysis".equals(node.getType())) {
            ImageAnalysisNodeConfigDto configDto = (ImageAnalysisNodeConfigDto) new NodeConfigProcessor(node).parseConfigDto();
            if (StringUtils.isNotBlank(configDto.getTemplateId())) {
                PromptEntity promptEntity = promptDao.selectOne(Wrappers.<PromptEntity>lambdaQuery().select(PromptEntity::getPromptGUID).eq(PromptEntity::getPromptGUID, configDto.getTemplateId()));
                if (promptEntity == null) {
                    throw new BusinessLogicException("提示词不存在");
                }
            }
            if("multimodal".equals(configDto.getRecognizeType())){
                if(!isPromptAvailable("4",configDto.getTemplateId(),true)){
                    throw new BusinessLogicException("节点【"+ node.getName() + "】只能选择绑定了图片服务实例的提示词或者是模型实例为空的提示词");
                }
            }
            if (!StringUtils.isEmpty(configDto.getOcrService())) {
                skillNodeEntity.setModelInstanceCode(configDto.getOcrService());
                saveSkillResource(skillGUID, null, configDto.getOcrService(), "ModelInstance", node.getId());
            }
            skillNodeEntity.setPromptGUID(configDto.getTemplateId());
            saveSkillResource(skillGUID, configDto.getTemplateId(), null, "PromptTemplate", node.getId());
        }
        if ("DocumentAnalysis".equals(node.getType())) {
            DocumentAnalysisNodeConfigDto configDto = (DocumentAnalysisNodeConfigDto) new NodeConfigProcessor(node).parseConfigDto();
            if (!StringUtils.isEmpty(configDto.getOcrService())) {
                skillNodeEntity.setModelInstanceCode(configDto.getOcrService());
                saveSkillResource(skillGUID, null, configDto.getOcrService(), "ModelInstance", node.getId());
            }
        }
        if ("Knowledge".equals(node.getType())) {
            KnowledgeNodeConfigDto configDto = (KnowledgeNodeConfigDto) new NodeConfigProcessor(node).parseConfigDto();
            if (CollectionUtils.isEmpty(configDto.getKnowledges())) {
                skillNodeEntity.setKnowledges("");
            } else {
                List<String> list = knowledgeDao.selectList(Wrappers.<KnowledgeEntity>lambdaQuery().select(KnowledgeEntity::getCode).in(KnowledgeEntity::getCode, configDto.getKnowledges())).
                        stream().map(KnowledgeEntity::getCode).collect(Collectors.toList());
                List<String> diff = configDto.getKnowledges().stream().filter(e -> !list.contains(e)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(diff)) {
                    throw new BusinessLogicException("知识库不存在:" + diff.stream().collect(Collectors.joining(",")));
                }
                skillNodeEntity.setKnowledges(JsonUtil.toString(configDto.getKnowledges()));
                saveSkillResource(skillGUID, null, JsonUtil.toString(configDto.getKnowledges()), "Knowledge", node.getId());
            }
        }
        if ("Plan".equals(node.getType())) {
            PlanNodeConfigDto configDto = (PlanNodeConfigDto) new NodeConfigProcessor(node).parseConfigDto();
            skillNodeEntity.setPlanGUID(configDto.getId());
            saveSkillResource(skillGUID, configDto.getId(), null,"Plan", node.getId());
        }
        return skillNodeEntity;
    }

    private Boolean isPromptAvailable(String serviceTypeEnum,String templateId,Boolean isDefault){
        List<ModelEntity> modelEntities = modelDao.selectList(new LambdaQueryWrapper<ModelEntity>()
                .eq(ModelEntity::getServiceTypeEnum,serviceTypeEnum));
        List<String> modelUUIDs = modelEntities.stream().map(ModelEntity::getModelGUID).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(modelUUIDs)){
            return false;
        }
        List<ModelInstanceEntity> modelInstanceEntities = modelInstanceDao.selectList(new LambdaQueryWrapper<ModelInstanceEntity>()
                .in(ModelInstanceEntity::getModelGUID,modelUUIDs));
        if(CollectionUtils.isEmpty(modelInstanceEntities)){
            return false;
        }
        List<String> modelInstanceCode = modelInstanceEntities.stream().map(ModelInstanceEntity::getInstanceCode).collect(Collectors.toList());
        LambdaQueryWrapper<PromptEntity> wrapper = new LambdaQueryWrapper<>();
        if(isDefault) {
            wrapper.and(
                    wrapper1 -> wrapper1.in(PromptEntity::getModelInstanceCode, modelInstanceCode)
                            .or()
                            .isNull(PromptEntity::getModelInstanceCode)
                            .or()
                            .eq(PromptEntity::getModelInstanceCode, "")
            );
        }
        else {
            wrapper.and(
                    wrapper1 -> wrapper1.in(PromptEntity::getModelInstanceCode, modelInstanceCode)
            );
        }
        wrapper.eq(PromptEntity::getPromptGUID,templateId);
        return promptDao.selectCount(wrapper) > 0;
    }

    /**
     * 删除技能节点数据。
     * 该方法会同时删除技能节点实体（skillNodeEntity）和技能编排实体（skillOrchestrationEntity）相关的数据。
     *
     * @param skillGUID 技能的全局唯一标识符，用于确定需要删除的技能节点。
     */
    private void skillNodeDelete(String skillGUID) {
        // 删除技能节点实体
        skillNodeDao.delete(new LambdaQueryWrapper<SkillNodeEntity>().eq(SkillNodeEntity::getSkillGUID, skillGUID));
        // 删除技能编排实体
        skillOrchestrationDao.delete(new LambdaQueryWrapper<SkillOrchestrationEntity>().eq(SkillOrchestrationEntity::getSkillGUID, skillGUID));
    }

    /**
     * 保存技能编排信息。
     * 该方法是一个私有方法，主要负责根据传入的请求DTO（数据传输对象）来保存技能编排的实体信息。
     *
     * @param requestDto 技能请求的DTO，包含了需要保存的技能编排的ID和流程信息。
     * @return 返回一个技能编排实体的列表，表示保存的结果。
     */
    private List<SkillOrchestrationContextDto> skillOrchestrationSave(SkillRequestDto requestDto) {
        // 调用同名方法，传入请求DTO中的ID和流程信息，进行保存操作
        return skillOrchestrationSave(requestDto.getId(), requestDto.getFlow());
    }

    /**
     * 保存技能编排信息
     *
     * @param skillGUID 技能的全局唯一标识符
     * @param flow      技能编排的流程数据对象
     * @return 返回保存后的技能编排实体列表
     * @throws BusinessLogicException 如果技能编排数据为空、节点或连接线数据为空，则抛出业务逻辑异常
     */
    private List<SkillOrchestrationContextDto> skillOrchestrationSave(String skillGUID, SkillRequestDto.FlowDTO flow) {
        // 验证传入的技能编排数据是否为空
        if (ObjectUtils.isEmpty(flow)) {
            throw new BusinessLogicException("技能编排数据为空");
        }
        List<SkillNodeDto> nodes = flow.getNodes(); // 获取节点列表
        List<SkillRequestDto.FlowDTO.EdgesDTO> edges = flow.getEdges(); // 获取连接线列表

        // 校验节点和连接线数据是否为空，确保编排的完整性和正确性
        if (CollectionUtils.isEmpty(nodes) || CollectionUtils.isEmpty(edges)) {
            throw new BusinessLogicException("技能编排节点或连接线数据为空");
        }

        Map<String, SkillNodeDto> nodeGroupByNodeCode = nodes.stream().collect(Collectors.toMap(SkillNodeDto::getCode, Function.identity()));

        //对接点进行分组
        Map<String, List<String>> edgesGroupBySource = edgesGroupBySource(edges);
        String StartNodeCode = edges.get(0).getSource();
        SkillOrchestrationContextDto skillOrchestrationContext = new SkillOrchestrationContextDto();
        skillOrchestrationContext.setCurrentOrchestrationGUID(UUID.randomUUID().toString());
        //从开始节点开始遍历
        AtomicInteger stepNumber = new AtomicInteger(1); // 用于编号每个节点的步骤
        createOrchestration(StartNodeCode, edgesGroupBySource, nodes, skillOrchestrationContext, stepNumber);
        return Lists.newArrayList(skillOrchestrationContext);
    }

    private void createOrchestration(String currentNodeCode, Map<String, List<String>> nodeGroupBySourceNodeCode, List<SkillNodeDto> nodes,
                                     SkillOrchestrationContextDto skillOrchestrationContext, AtomicInteger stepNumber) {
        List<SkillNodeDto> currentNodes = nodes.stream().filter(node -> node.getCode().equals(currentNodeCode)).collect(Collectors.toList());
        SkillNodeDto node = currentNodes.get(0);
        //下级节点
        List<String> targetNodeCodes = nodeGroupBySourceNodeCode.get(currentNodeCode);
        if ("Start".equals(node.getType())) {
            skillOrchestrationContext.setCurrentOrchestrationGUID(UUID.randomUUID().toString());// 为当前编排生成唯一标识
            NodeConfigProcessor nodeProcessor = new NodeConfigProcessor(skillOrchestrationContext, stepNumber.get(), node, Lists.newArrayList());
            // 生成单个节点的编排数据
            String sourceOrchestration = nodeProcessor.createOrchestration();
            SkillOrchestrationDto skillOrchestrationDto = new SkillOrchestrationDto();
            skillOrchestrationDto.setOrchestraionTemplate(sourceOrchestration);
            skillOrchestrationContext.setSkillOrchestrationDto(skillOrchestrationDto);
            stepNumber.getAndIncrement(); // 更新步骤编号
            String targetNodeCode = targetNodeCodes.get(0);
            createOrchestration(targetNodeCode, nodeGroupBySourceNodeCode, nodes, skillOrchestrationContext, stepNumber);
        } else if ("Selector".equals(node.getType())) {
            //获取选择器节点的模版数据
            NodeConfigProcessor nodeProcessor = new NodeConfigProcessor(skillOrchestrationContext, stepNumber.get(), node, Lists.newArrayList());
            // 生成单个节点的编排数据
            String selectorOrchestration = nodeProcessor.createOrchestration();
            SkillOrchestrationDto skillOrchestrationDto = skillOrchestrationContext.getSkillOrchestrationDto();
            skillOrchestrationDto.setOrchestraionTemplate(skillOrchestrationDto.getOrchestraionTemplate() + selectorOrchestration);
            skillOrchestrationContext.setSkillOrchestrationDto(skillOrchestrationDto);
            stepNumber.getAndIncrement(); // 更新步骤编号
            // 这里的目标节点的值在config 的 condition 里面， 这里是多个树的结构
            SelectorNodeConfigDto selectorNodeConfigDto = (SelectorNodeConfigDto) nodeProcessor.parseConfigDto();
            List<String> newTargetNodeCodes = selectorNodeConfigDto.getConditions().stream().filter(conditionsDTO -> StringUtils.isNotBlank(conditionsDTO.getTarget())).map(conditionsDTO -> conditionsDTO.getTarget()).collect(Collectors.toList());
            for (String targetNodeCode : newTargetNodeCodes) {
                SkillOrchestrationDto skillOrchestrationDtoChild = skillOrchestrationContext.getSkillOrchestrationDto();
                String newTemplate = skillOrchestrationDtoChild.getOrchestraionTemplate() + "\n{{set \"NodeOutput_" + currentNodeCode + "_" + targetNodeCode + "\"  \"" + targetNodeCode + "\"}}\n ";
                skillOrchestrationDtoChild.setOrchestraionTemplate(newTemplate + "{{#if (equals NodeOutput_" + currentNodeCode + " NodeOutput_" + currentNodeCode + "_" + targetNodeCode + ")}}\n ");
                skillOrchestrationContext.setSkillOrchestrationDto(skillOrchestrationDtoChild);
                //添加选择的 IF 判断语句
                createOrchestration(targetNodeCode, nodeGroupBySourceNodeCode, nodes, skillOrchestrationContext, stepNumber);
                stepNumber.getAndIncrement();
                //添加 IF 结束的语句
                SkillOrchestrationDto skillOrchestrationDtoChildEnd = skillOrchestrationContext.getSkillOrchestrationDto();
                skillOrchestrationDtoChildEnd.setOrchestraionTemplate(skillOrchestrationDtoChildEnd.getOrchestraionTemplate() + "{{/if}}\n ");
                skillOrchestrationContext.setSkillOrchestrationDto(skillOrchestrationDtoChildEnd);
            }
            if (!CollectionUtils.isEmpty(targetNodeCodes)) {
                String targetNodeCode = targetNodeCodes.get(0);
                stepNumber.getAndIncrement(); // 更新步骤编号
                createOrchestration(targetNodeCode, nodeGroupBySourceNodeCode, nodes, skillOrchestrationContext, stepNumber);
            }
        } else {
            //如果当前节点是卡片节点需要获取下个节点的编码
            if ("Card".equals(node.getType()) || "Plan".equals(node.getType())) {
                getNextNodeCode(currentNodeCode, nodeGroupBySourceNodeCode, nodes, skillOrchestrationContext);
            }
            NodeConfigProcessor nodeProcessor = new NodeConfigProcessor(skillOrchestrationContext, stepNumber.get(), node, Lists.newArrayList(skillOrchestrationContext.getCurrentOrchestrationGUID()));
            // 生成单个节点的编排数据
            String sourceOrchestration = nodeProcessor.createOrchestration();
            SkillOrchestrationDto skillOrchestrationDto = skillOrchestrationContext.getSkillOrchestrationDto();
            skillOrchestrationDto.setOrchestraionTemplate(skillOrchestrationDto.getOrchestraionTemplate() + sourceOrchestration);
            skillOrchestrationContext.setSkillOrchestrationDto(skillOrchestrationDto);
            if (!CollectionUtils.isEmpty(targetNodeCodes)) {
                String targetNodeCode = targetNodeCodes.get(0);
                stepNumber.getAndIncrement(); // 更新步骤编号
                createOrchestration(targetNodeCode, nodeGroupBySourceNodeCode, nodes, skillOrchestrationContext, stepNumber);
            }
        }
        return;
    }

    /**
     * 获取当前节点的下个节点编码
     *
     * @param currentNodeCode
     * @param nodeGroupBySourceNodeCode
     * @param nodes
     * @return
     */
    private String getNextNodeCode(String currentNodeCode, Map<String, List<String>> nodeGroupBySourceNodeCode, List<SkillNodeDto> nodes,
                                   SkillOrchestrationContextDto skillOrchestrationContext) {
        List<String> nextNodeCodes = nodeGroupBySourceNodeCode.get(currentNodeCode);
        if (!CollectionUtils.isEmpty(nextNodeCodes)) {
            String nextNodeCode = nextNodeCodes.get(0);
            if (!StringUtils.isEmpty(nextNodeCode)) {
                SkillNodeDto nextNode = nodes.stream().filter(f -> nextNodeCode.equals(f.getCode())).collect(Collectors.toList()).get(0);
                skillOrchestrationContext.setNextNodeGUID(nextNode.getId());
                return nextNode.getId();
            }
        }
        SkillNodeDto node = nodes.stream().filter(f -> currentNodeCode.equals(f.getCode())).findFirst().orElse(null);
        String parentNode = node.getParentNode();
        return getNextNodeCode(parentNode, nodeGroupBySourceNodeCode, nodes, skillOrchestrationContext);
    }


    /**
     * 根据节点组列表创建编排实体列表。
     *
     * @param nodeGroupList 节点组列表，不能为null或空。每个节点组包含一系列的SkillNodeDto对象。
     * @return 返回一个包含SkillOrchestrationEntity对象的列表，每个对象代表一个编排实体。
     * @throws BusinessLogicException 如果节点组列表为null或空，或者内部节点组为null或空，则抛出此异常。
     */
    private List<SkillOrchestrationContextDto> createOrchestration(ArrayList<ArrayList<SkillNodeDto>> nodeGroupList) {
        // 检查输入的节点组列表是否合法
        if (nodeGroupList == null || nodeGroupList.isEmpty()) {
            throw new BusinessLogicException("Node groups cannot be null or empty");
        }
        List<SkillOrchestrationContextDto> skillOrchestrationContextDto = new ArrayList<>();
        ArrayList<String> orchestrationGUIDs = new ArrayList<>(nodeGroupList.size());
        // 遍历每一个节点组，生成编排实体
        nodeGroupList.forEach(nodeGroup -> {
            // 再次检查每个节点组的合法性
            if (nodeGroup == null || nodeGroup.isEmpty()) {
                throw new BusinessLogicException("Node group cannot be null or empty");
            }
            AtomicInteger stepNumber = new AtomicInteger(1); // 用于编号每个节点的步骤
            List<String> groupTemplate = new ArrayList<>(); // 存储当前节点组生成的编排模板字符串

            SkillOrchestrationContextDto orchestrationContextDto = new SkillOrchestrationContextDto();
            orchestrationContextDto.setCurrentOrchestrationGUID(UUID.randomUUID().toString());// 为当前编排生成唯一标识
            orchestrationGUIDs.add(orchestrationContextDto.getCurrentOrchestrationGUID());
            // 遍历当前节点组，生成编排数据
            nodeGroup.forEach(node -> {
                NodeConfigProcessor nodeProcessor = new NodeConfigProcessor(orchestrationContextDto, stepNumber.get(), node, orchestrationGUIDs);

                // 生成单个节点的编排数据
                String orchestration = nodeProcessor.createOrchestration();
                // 过滤掉空或"null"的编排数据
                if (orchestration != null && !orchestration.isEmpty() && !"null".equals(orchestration)) {
                    groupTemplate.add(orchestration);
                }

                stepNumber.getAndIncrement(); // 更新步骤编号
            });

            // 将同一节点组的编排数据拼接成一个大的模板字符串
            String orchestrationTemplate = buildOrchestrationTemplate(groupTemplate);
            // 根据拼接的模板字符串和当前节点组信息，构建SkillOrchestrationEntity对象
            SkillOrchestrationDto skillOrchestrationDto = buildOrchestrationDto(orchestrationContextDto, nodeGroup, orchestrationTemplate);
            orchestrationContextDto.setSkillOrchestrationDto(skillOrchestrationDto);
            skillOrchestrationContextDto.add(orchestrationContextDto); // 将构建的实体添加到列表中
        });
        return skillOrchestrationContextDto;
    }

    /**
     * 构建一个字符串，将输入的字符串列表按行拼接起来。
     * 这个方法使用StringBuilder来高效地处理大量的字符串拼接操作。
     *
     * @param groupTemplate 包含需要拼接成一个模板的字符串的列表。
     * @return 拼接后的字符串，每个字符串之间以换行符分隔。
     */
    private String buildOrchestrationTemplate(List<String> groupTemplate) {
        // 使用StringBuilder来高效处理字符串拼接
        StringBuilder builder = new StringBuilder();
        for (String template : groupTemplate) {
            builder.append(template).append("\n"); // 逐行拼接字符串，并在每行末尾添加换行符
        }
        return builder.toString();
    }

    /**
     * 构建技能编排实体。
     *
     * @param orchestrationGUID     编排的全局唯一标识符。
     * @param nodeGroup             技能节点列表，表示编排中的节点组。
     * @param orchestrationTemplate 编排模板，定义了编排的具体逻辑。
     * @return 返回构建好的SkillOrchestrationEntity实体对象。
     */
    private SkillOrchestrationDto buildOrchestrationDto(SkillOrchestrationContextDto orchestrationGUID, List<SkillNodeDto> nodeGroup, String orchestrationTemplate) {
        SkillOrchestrationDto entity = new SkillOrchestrationDto();
        // 设置编排的全局唯一标识符
        entity.setSkillOrchestraionGUID(orchestrationGUID.getCurrentOrchestrationGUID());
        // 设置起始节点GUID，使用节点组中的第一个节点的代码
        entity.setStartNodeGUID(nodeGroup.get(0).getCode());
        // 设置结束节点GUID，使用节点组中的最后一个节点的代码
        entity.setEndNodeGUID(nodeGroup.get(nodeGroup.size() - 1).getCode());
        // 设置编排模板
        entity.setOrchestraionTemplate(orchestrationTemplate);
        return entity;
    }

    /**
     * 根据节点代码顺序列表和按节点代码分组的节点映射，将节点分组。
     *
     * @param nodeCodeOrderList   节点代码顺序列表，用于指定节点的排列顺序。确保了节点的处理顺序。
     * @param nodeGroupByNodeCode 按节点代码分组的节点映射，键为节点代码，值为对应的节点信息。提供了节点信息的查找能力。
     * @return 分组后的节点列表，每个分组包含按指定顺序排列的节点。返回的列表中，每个内部列表代表一个独立的节点分组。
     * 分组顺序与nodeCodeOrderList参数中指定的顺序一致。
     */
    private ArrayList<ArrayList<SkillNodeDto>> splitNodeFragments(List<String> nodeCodeOrderList, Map<String, SkillNodeDto> nodeGroupByNodeCode) {
        // 检查输入参数是否为null，确保方法能够安全进行。
        if (nodeCodeOrderList == null || nodeGroupByNodeCode == null) {
            throw new IllegalArgumentException("nodeCodeOrderList and nodeGroupByNodeCode must not be null.");
        }

        // 初始化用于存放分组节点的列表，最终返回此列表。
        ArrayList<ArrayList<SkillNodeDto>> groupedNodes = new ArrayList<>();
        // 当前分组，用于动态收集按顺序遍历过程中的节点。随着遍历过程动态更新。
        ArrayList<SkillNodeDto> currentGroup = new ArrayList<>();

        // 遍历节点代码顺序列表，根据节点类型进行不同的处理
        for (int i = 0; i < nodeCodeOrderList.size(); i++) {
            String nodeCode = nodeCodeOrderList.get(i);
            // 尝试获取当前节点信息，基于节点代码。
            SkillNodeDto skillNode = nodeGroupByNodeCode.get(nodeCode);
            // 如果节点类型为"Card"，则进行特殊处理。表明节点需要独立分组。
            //TODO 这里的切片规则后续应该是可以自定义的
            if ("Card".equals(skillNode.getType()) && "interactive".equals(skillNode.getConfig().get("type"))) {
                processCardNode(skillNode, groupedNodes, currentGroup);
                // 开始新的分组，以处理下一个非"Card"类型的节点。
                currentGroup = new ArrayList<>();
            } else {
                // 否则，将其加入当前分组。确保节点按预定顺序加入当前活动分组。
                addToCurrentGroup(skillNode, currentGroup);
            }

        }
        // 处理最后一个分组，确保所有节点都被收集到相应的分组中。
        if (currentGroup.size() > 0) {
            groupedNodes.add(currentGroup);
        }
        return groupedNodes;
    }

    /**
     * 处理遇到的CARD类型节点。
     * 此方法用于处理技能树中的CARD类型节点，将该节点加入到当前分组中，并将当前分组加入到已分组的节点列表中。
     *
     * @param skillNodeDto 单个节点数据对象。表示当前需要处理的CARD类型节点的具体信息。
     * @param groupedNodes 已分组的节点列表。用于存储所有已经分组的节点，每个分组包含多个节点。
     * @param currentGroup 当前正在处理的节点分组。如果当前没有正在处理的分组，则方法会创建一个新的分组。
     */
    private void processCardNode(SkillNodeDto skillNodeDto, ArrayList<ArrayList<SkillNodeDto>> groupedNodes, ArrayList<SkillNodeDto> currentGroup) {
        // 如果当前没有正在处理的节点分组，则创建一个新的分组
        if (currentGroup == null) {
            currentGroup = new ArrayList<>();
        }
        // 将当前节点加入到当前分组中
        currentGroup.add(skillNodeDto);
        // 将当前分组加入到已分组的节点列表中
        groupedNodes.add(currentGroup);
    }

    /**
     * 将节点添加到当前的分组中。
     * 这个方法检查当前分组是否为null，如果是，则创建一个新的分组，并将给定的节点添加到这个新分组中；
     * 如果当前分组不为null，则直接将节点添加到当前分组中。
     *
     * @param skillNodeDto 单个节点数据对象，是要被添加到分组中的节点。
     * @param currentGroup 当前正在处理的节点分组，是接收新节点的分组。
     */
    private void addToCurrentGroup(SkillNodeDto skillNodeDto, ArrayList<SkillNodeDto> currentGroup) {
        // 检查当前分组是否为空，若为空则初始化分组
        if (currentGroup == null) {
            currentGroup = new ArrayList<>();
        }
        // 将节点添加到当前分组
        currentGroup.add(skillNodeDto);
    }

    /**
     * 根据给定的边列表组装顺序序列。
     *
     * @param edges 边的列表，每个边包含源节点和目标节点信息。
     * @return 返回一个顺序序列列表，列表中的节点按照图中边的顺序进行排列。
     * @throws IllegalArgumentException 如果边或其源目标节点为空，或者图中存在环，或者存在重复的源边时抛出异常。
     */
    public Map<String, List<String>> edgesGroupBySource(List<SkillRequestDto.FlowDTO.EdgesDTO> edges) {
        // 检查输入边列表是否为空或空列表
        if (edges == null || edges.isEmpty()) {
            throw new IllegalArgumentException("节点连接线不能为空");
        }

        // 对边的非空和源目标非空进行校验
        edges.stream().forEach(edge -> {
            if (edge == null || edge.getSource() == null || edge.getTarget() == null) {
                throw new IllegalArgumentException("连接线的source/target不能为空");
            }
        });

        // 构建边关系的映射，以便快速查找
        Map<String, List<String>> edgeMap = new HashMap<>();
        for (SkillRequestDto.FlowDTO.EdgesDTO edge : edges) {
            // 如果后续支持分支节点的逻辑，此处需要进行相应的调整
            if (edgeMap.containsKey(edge.getSource())) {
                List<String> edgesDTOS = edgeMap.get(edge.getSource());
                edgesDTOS.add(edge.getTarget());
                edgeMap.put(edge.getSource(), edgesDTOS);
            } else {
                edgeMap.put(edge.getSource(), Lists.newArrayList(edge.getTarget()));
            }
        }
        return edgeMap;
    }


    /**
     * 根据给定的边列表组装顺序序列。
     *
     * @param edges 边的列表，每个边包含源节点和目标节点信息。
     * @return 返回一个顺序序列列表，列表中的节点按照图中边的顺序进行排列。
     * @throws IllegalArgumentException 如果边或其源目标节点为空，或者图中存在环，或者存在重复的源边时抛出异常。
     */
    public ArrayList<String> assembleSequence(List<SkillRequestDto.FlowDTO.EdgesDTO> edges) {
        // 检查输入边列表是否为空或空列表
        if (edges == null || edges.isEmpty()) {
            return Lists.newArrayList();
        }

        // 对边的非空和源目标非空进行校验
        edges.stream().forEach(edge -> {
            if (edge == null || edge.getSource() == null || edge.getTarget() == null) {
                throw new IllegalArgumentException("Edge or its source/target should not be null.");
            }
        });

        // 构建边关系的映射，以便快速查找
        Map<String, String> edgeMap = new HashMap<>();
        for (SkillRequestDto.FlowDTO.EdgesDTO edge : edges) {
            // 如果后续支持分支节点的逻辑，此处需要进行相应的调整
            if (edgeMap.containsKey(edge.getSource())) {
                throw new IllegalArgumentException("Duplicate source edge detected: " + edge.getSource());
            }
            edgeMap.put(edge.getSource(), edge.getTarget());
        }

        // 初始化未访问的节点集合
        Set<String> unvisitedNodes = new HashSet<>(edgeMap.values());
        // 初始化结果列表
        ArrayList<String> result = new ArrayList<>();
        // 从第一个边的源节点开始
        String currentNode = edges.get(0).getSource();
        result.add(currentNode);
        unvisitedNodes.remove(currentNode); // 移除已访问的节点

        // 使用队列来进行广度优先搜索，并检测环
        Queue<String> nodeQueue = new LinkedList<>();
        nodeQueue.offer(currentNode);

        // 广度优先搜索遍历
        while (!nodeQueue.isEmpty()) {
            currentNode = nodeQueue.poll();
            // 检查当前节点是否已经被访问过，若已访问则说明存在环
            if (unvisitedNodes.contains(currentNode)) {
                throw new IllegalArgumentException("Cycle detected in the graph.");
            }
            // 获取当前节点的下一个节点
            String nextNode = edgeMap.get(currentNode);
            if (nextNode != null) {
                result.add(nextNode);
                unvisitedNodes.remove(nextNode);
                nodeQueue.offer(nextNode);
            }
        }
        return result;
    }

    /**
     * 发布技能的私有方法。
     * 该方法通过接收技能请求DTO（数据传输对象）和技能编排实体列表，来保存发布技能的相关数据。
     *
     * @param requestDto                   技能请求的DTO，包含技能发布的详细信息。
     * @param skillOrchestrationContextDto 技能编排实体的列表，用于存储与技能发布相关的编排信息。
     */
    private void publishSkill(SkillRequestDto requestDto, List<SkillOrchestrationContextDto> skillOrchestrationContextDto) {
        // 调用publishSkill方法，传入请求ID、请求DTO和编排实体列表，以保存发布技能的数据
        publishSkill(requestDto.getId(), requestDto, skillOrchestrationContextDto);
    }

    /**
     * 发布技能信息。
     * 该方法负责保存技能的发布信息，包括技能的元数据和编排信息。
     * 技能的发布信息将会被持久化到数据库中。
     *
     * @param skillGUID                     技能的全局唯一标识符。
     * @param requestDto                    技能请求的DTO（数据传输对象），包含技能的详细请求信息和编排信息。
     * @param skillOrchestrationContextDtos 技能编排实体的列表，描述了技能的编排细节。
     * @throws Exception 如果发布过程中出现任何错误，则会抛出异常。
     */
    @Transactional(rollbackFor = Exception.class)
    public void publishSkill(String skillGUID, SkillRequestDto requestDto, List<SkillOrchestrationContextDto> skillOrchestrationContextDtos) {
        //先删除以前的历史版本
        LambdaQueryWrapper<PublishedSkillEntity> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(PublishedSkillEntity::getSkillGUID, skillGUID);
        publishedSkillDao.delete(deleteWrapper);

        // 生成并保存发布技能的实体信息
        PublishedSkillEntity publishedSkillEntity = new PublishedSkillEntity();
        publishedSkillEntity.setPublishedSkillGUID(UUID.randomUUID().toString());
        publishedSkillEntity.setSkillGUID(skillGUID);

        // 根据技能GUID查询技能实体，获取当前技能的版本信息
        SkillEntity skillEntity = skillDao.selectOne(new LambdaQueryWrapper<SkillEntity>().eq(SkillEntity::getSkillGUID, skillGUID));
        publishedSkillEntity.setSkillVersions(skillEntity.getSkillVersions());
        if (SkillModeConst.FLOW.equals(requestDto.getMode()) && !CollectionUtils.isEmpty(skillOrchestrationContextDtos)) {
            // 将编排实体信息转换为DTO中的编排元数据列表
            List<SkillRequestDto.OrchestrationMetadata> orchestrationList = skillOrchestrationContextDtos.stream()
                    .filter(c -> !StringUtils.isEmpty(c.getSkillOrchestrationDto().getOrchestraionTemplate()))
                    .map(skillOrchestrationContextDto -> {
                        SkillOrchestrationDto skillOrchestrationDto = skillOrchestrationContextDto.getSkillOrchestrationDto();
                        SkillRequestDto.OrchestrationMetadata orchestrationMetadata = new SkillRequestDto.OrchestrationMetadata();
                        orchestrationMetadata.setId(skillOrchestrationContextDto.getCurrentOrchestrationGUID());
                        orchestrationMetadata.setOrchestrationTemplate(skillOrchestrationDto.getOrchestraionTemplate());
                        orchestrationMetadata.setPrompts(skillOrchestrationContextDto.getPrompts());
                        orchestrationMetadata.setPlugins(skillOrchestrationContextDto.getPlugins());
                        return orchestrationMetadata;
                    }).collect(Collectors.toList());

            // 更新DTO中的编排信息，并将整个DTO序列化后保存为技能的元数据
            requestDto.setOrchestrations(orchestrationList);
        }
        publishedSkillEntity.setMetadata(JsonUtil.toString(requestDto));
        publishedSkillDao.insert(publishedSkillEntity);
    }

    /**
     * 删除与给定技能GUID相关的所有数据。
     * 这包括技能实体、技能节点、技能节点元数据、技能编排和已发布的技能。
     * 如果在删除过程中发生任何异常，将抛出一个运行时异常。
     *
     * @param skillGUID 技能的全局唯一标识符
     * @return 总是返回true，表示删除操作完成
     * @throws RuntimeException 当删除过程中出现异常时抛出
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String skillGUID) {
        AssistantEntity assistantEntity = assistantSkillDao.selectBySkillGUID(skillGUID);
        if (assistantEntity != null) {
            throw new BusinessLogicException(MessageFormat.format("已经被助手 {0} 使用，无法删除", assistantEntity.getAssistantName()));
        }
        checkPlanUser(skillGUID);

        // 检查是否被评测引用
        String allEvalTaskName = evalTaskDao.selectList(Wrappers.<EvalTaskEntity>lambdaQuery().select(EvalTaskEntity::getTaskName)
                        .eq(EvalTaskEntity::getEvalObject, skillGUID))
                .stream().map(EvalTaskEntity::getTaskName).collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(allEvalTaskName)) {
            throw new BusinessLogicException(MessageFormat.format("已经被评测任务 {0} 使用，无法删除", allEvalTaskName));
        }

        SkillEntity skillEntity = skillDao.selectOne(new LambdaQueryWrapper<SkillEntity>().eq(SkillEntity::getSkillGUID, skillGUID));
        if (ObjectUtils.isEmpty(skillEntity)) {
            throw new BusinessLogicException("技能已被删除，无需重复操作");
        }
        if (skillEntity.getIsSystem() == 1) {
            //系统技能不允许进行修改、允许新增系统级技能
            throw new BusinessLogicException("系统级技能不允许删除");
        }
        try {
            // 删除技能实体
            skillDao.delete(new LambdaQueryWrapper<SkillEntity>().eq(SkillEntity::getSkillGUID, skillGUID));
            // 删除技能节点
            skillNodeDao.delete(new LambdaQueryWrapper<SkillNodeEntity>().eq(SkillNodeEntity::getSkillGUID, skillGUID));

            // 删除技能节点元数据
            skillNodeMetadataDao.delete(new LambdaQueryWrapper<SkillNodeMetadataEntity>().eq(SkillNodeMetadataEntity::getSkillGUID, skillGUID));

            // 删除技能编排信息
            skillOrchestrationDao.delete(new LambdaQueryWrapper<SkillOrchestrationEntity>().eq(SkillOrchestrationEntity::getSkillGUID, skillGUID));

            // 删除发布的技能信息
            publishedSkillDao.delete(new LambdaQueryWrapper<PublishedSkillEntity>().eq(PublishedSkillEntity::getSkillGUID, skillGUID));

            //删除技能历史版本
            skillVersionsDao.delete(new LambdaQueryWrapper<SkillVersionsEntity>().eq(SkillVersionsEntity::getSkillGUID, skillGUID));

            // 删除技能资源关联数据（包括 tools、mcps、knowledges 等）
            skillResourceDao.delete(new LambdaQueryWrapper<SkillResourceEntity>().eq(SkillResourceEntity::getSkillGUID, skillGUID));
        } catch (Exception e) {
            throw new BusinessLogicException("技能关联数据删除异常");
        }
        return true;
    }

    public void checkPlanUser(String skillGUID) {
        List<String> planGUIDs = planDatasourceDao.selectList(new LambdaQueryWrapper<PlanDatasourceEntity>()
                .select(PlanDatasourceEntity::getPlanGUID).eq(PlanDatasourceEntity::getSourceId, skillGUID))
                .stream().map(PlanDatasourceEntity::getPlanGUID).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(planGUIDs)) {
            String allName = planDao.selectList(Wrappers.<PlanEntity>lambdaQuery()
                    .select(PlanEntity::getPlanName).in(PlanEntity::getPlanGUID, planGUIDs))
                    .stream().map(PlanEntity::getPlanName).collect(Collectors.joining(","));
            if (!StringUtils.isEmpty(allName)) {
                throw new BusinessLogicException(MessageFormat.format("已经被检查方案 {0} 使用，无法删除", allName));
            }
        }
    }

    /**
     * 复制技能功能实现。
     * 从提供的技能复制请求DTO中获取信息，复制并创建一个新的技能实体。
     *
     * @param copyDto 技能复制请求数据传输对象，包含复制技能的GUID等信息。
     * @return 返回新创建技能的GUID。
     * @throws BusinessLogicException 如果输入参数为空、复制的技能不存在、或复制验证失败时抛出。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String copySkill(SkillCopyRequestDto copyDto) {
        if(!BusinessControlHelper.IsStandAlone()){
            throw new BusinessLogicException("复制失败：当前版本不允许新增技能，请联系系统管理员。");
        }
        // 校验输入参数不能为空
        if (ObjectUtils.isEmpty(copyDto) || ObjectUtils.isEmpty(copyDto.getCopySkillGUID())) {
            throw new BusinessLogicException("复制技能参数不能为空");
        }
        // 查询原技能实体
        SkillEntity skillEntity = skillDao.selectOne(new LambdaQueryWrapper<SkillEntity>().eq(SkillEntity::getSkillGUID, copyDto.getCopySkillGUID()));

        // 判断原技能实体是否存在
        if (ObjectUtils.isEmpty(skillEntity)) {
            throw new BusinessLogicException("复制技能不存在");
        }

        // 验证复制请求DTO的合法性及技能编码的唯一性
        copyDto.setNewSkillCode(workSpaceService.generatePrefixCode(skillEntity.getSpaceGUID(), copyDto.getNewSkillCode()));
        copyValidate(copyDto, skillEntity);

        // 准备并初始化新的技能实体
        SkillRequestDto skillRequestDto = prepareSkillNode(copyDto, skillEntity);
        // 保存或更新技能实体到数据库
        skillDao.insert(skillEntity);
        // 保存预置问题
        saveSkillQuestion(skillRequestDto);
        saveSkillUserExample(skillRequestDto);
        if (ObjectUtils.isEmpty(skillRequestDto) || (ObjectUtils.isEmpty(skillRequestDto.getFlow())
                && ObjectUtils.isEmpty(skillRequestDto.getAgent()))) {
            // 如果DTO为空或没有节点数据，直接返回null，表示无需保存节点信息
            return skillEntity.getSkillGUID();
        }

        handleSkillNode(skillRequestDto, false, false);
        handleSkillPublishing(skillRequestDto);
//        //删除技能子表数据
//        deleteSkillNodeDto(skillRequestDto, Boolean.TRUE);
//        // 节点数据保存
//        skillNodeSave(skillRequestDto, Boolean.TRUE, Boolean.FALSE);
//        // 处理技能的发布流程
//        handleSkillPublishing(copyDto, skillRequestDto);
        return skillEntity.getSkillGUID();
    }

    /**
     * 准备技能节点数据，用于技能复制功能。
     * 该方法主要处理技能实体的初始化及元数据的处理，根据条件决定是否需要保存节点数据。
     *
     * @param copyDto     复制请求DTO，包含新技能的代码和名称。
     * @param skillEntity 技能实体，用于存储技能信息。
     * @return SkillRequestDto 如果需要保存节点数据，则返回包含更新后信息的SkillRequestDto对象；否则返回null。
     */
    private SkillRequestDto prepareSkillNode(SkillCopyRequestDto copyDto, SkillEntity skillEntity) {
        // 初始化技能实体的基本信息
        skillEntity.setSkillGUID(UUID.randomUUID().toString());
        skillEntity.setSkillCode(copyDto.getNewSkillCode());
        skillEntity.setSkillName(copyDto.getNewSkillName());
        skillEntity.setSkillVersions("V001");
        skillEntity.setIsSystem(0);

        // 获取并处理技能实体的元数据
        String nodeFlowMetadata = skillEntity.getMetadata();

        if (StringUtils.isEmpty(nodeFlowMetadata)) {
            // 如果元数据为空，则不处理节点数据，直接返回null
            return null;
        }

        // 将元数据转换为DTO，用于进一步处理
        SkillRequestDto skillRequestDto = SkillRequestDto.coverToDto(skillEntity.getMetadata());

        // 更新DTO的ID、代码和名称，并保存节点数据到数据库
        skillRequestDto.setId(skillEntity.getSkillGUID());
        skillRequestDto.setCode(skillEntity.getSkillCode());
        skillRequestDto.setName(skillEntity.getSkillName());
        // 更新技能实体的元数据字段
        skillEntity.setMetadata(JsonUtil.toString(skillRequestDto));
        if (ObjectUtils.isEmpty(skillRequestDto) || ObjectUtils.isEmpty(skillRequestDto.getFlow())
                || ObjectUtils.isEmpty(skillRequestDto.getFlow().getNodes())) {
            // 如果DTO为空或没有节点数据，直接返回null，表示无需保存节点信息
            return skillRequestDto;
        }
        if (SkillModeConst.FLOW.equals(skillRequestDto.getMode())) {
            skillNodeInsert(skillRequestDto, false);
        }
        return skillRequestDto;
    }

    /**
     * 处理技能发布流程。
     *
     * @param copyDto         复制技能请求数据传输对象，包含要复制的技能的GUID。
     * @param skillRequestDto 技能请求数据传输对象，包含技能的详细信息。
     *                        <p>
     *                        本方法首先尝试根据复制技能的GUID检索已发布的技能实体列表。
     *                        如果找到了相关技能实体，则进一步进行技能的编排数据解析和发布流程。
     */
    private void handleSkillPublishing(SkillCopyRequestDto copyDto, SkillRequestDto skillRequestDto) {
        // 根据复制的技能GUID查询已发布的技能实体列表
        List<PublishedSkillEntity> copeSkillPublishedSkillEntities = publishedSkillDao.selectList(
                new LambdaQueryWrapper<PublishedSkillEntity>().eq(PublishedSkillEntity::getSkillGUID, copyDto.getCopySkillGUID()));

        if (CollectionUtils.isEmpty(copeSkillPublishedSkillEntities)) {
            return; // 如果没有找到待发布的技能数据，则直接返回
        }

        // 解析技能编排数据并生成技能编排实体列表
        List<SkillOrchestrationContextDto> skillOrchestrationContextDto = skillOrchestrationSave(skillRequestDto);
        // 执行技能发布操作，包括处理编排实体列表
        publishSkill(skillRequestDto, skillOrchestrationContextDto);
    }

    /**
     * 校验复制技能时的请求参数，并进行逻辑校验。
     *
     * @param copyDto     包含复制技能所需的请求参数，如原技能GUID、新技能名称和新技能编码。
     * @param skillEntity 需要被复制的技能实体，包含原技能的信息。
     * @throws BusinessLogicException 如果复制参数为空、新技能编码与原技能编码相同，或新技能编码已存在，则抛出业务逻辑异常。
     */
    private void copyValidate(SkillCopyRequestDto copyDto, SkillEntity skillEntity) {
        // 校验复制请求参数完整性及新技能编码的唯一性
        if (ObjectUtils.isEmpty(copyDto.getCopySkillGUID()) || ObjectUtils.isEmpty(copyDto.getNewSkillName()) || ObjectUtils.isEmpty(copyDto.getNewSkillCode())) {
            throw new BusinessLogicException("复制技能参数不能为空");
        }

        if (skillEntity.getSkillCode().equals(copyDto.getNewSkillCode())) {
            throw new BusinessLogicException("新技能编码不能与原技能编码相同");
        }

        // 检查新技能编码在数据库中是否已存在
        SkillEntity newSkillEntity = skillDao.selectOne(new LambdaQueryWrapper<SkillEntity>().eq(SkillEntity::getSkillCode, copyDto.getNewSkillCode()));
        if (!ObjectUtils.isEmpty(newSkillEntity)) {
            throw new BusinessLogicException("新技能编码已存在");
        }
    }

    /**
     * 获取已发布的技能的GUID列表。
     * 此方法不接受任何参数，它通过查询数据库来获取已发布的技能的GUID列表。
     * 查询时会限制结果集的大小为5条。
     *
     * @return 返回一个包含已发布技能GUID的字符串列表。
     */
    private List<String> fetchSkillGUIDs() {
        // 使用LambdaQueryWrapper构建查询条件，查询已发布状态的技能
        LambdaQueryWrapper<PublishedSkillEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PublishedSkillEntity::getPublishStatus, PublishStatusEnum.PUBLISHED.getValue());
        wrapper.last("limit 5"); // 设置查询结果限制为最近的5条已发布技能

        // 执行数据库查询操作，获取符合条件的已发布技能实体列表
        List<PublishedSkillEntity> publishedSkills = publishedSkillDao.selectList(wrapper);

        // 对查询结果进行处理，提取每个技能的GUID，并集合为列表返回
        return publishedSkills.stream().map(PublishedSkillEntity::getPublishedSkillGUID).collect(Collectors.toList());
    }

    /**
     * 根据GUID列表查询技能实体。
     * <p>
     * 该方法通过提供的技能GUID列表，从数据库中查询对应的技能实体。如果提供的GUID列表为空或null，将返回一个空的技能实体列表，避免后续操作出现异常。
     *
     * @param skillGUIDs 技能的GUID列表，用于查询对应的技能实体。
     * @return 技能实体列表，如果查询不到任何实体或输入为空，返回空的列表。
     */
    private List<SkillEntity> fetchSkillEntitiesByGUIDs(List<String> skillGUIDs) {
        // 验证输入列表是否为空，避免空或null的列表导致的后续问题
        if (skillGUIDs == null || skillGUIDs.isEmpty()) {
            return new ArrayList<>();
        }
        // 批量查询技能实体
        return skillDao.selectBatchIds(skillGUIDs);
    }

    /**
     * 获取系统参数列表。
     * <p>此方法不接受任何参数，返回系统参数的集合。</p>
     *
     * @return List<SystemParamDto> 返回系统参数的DTO（数据传输对象）列表。
     */
    @Override
    public List<SystemParamGroupDto> getSystemParam() {
        // 从系统参数常量中获取所有参数
        List<SystemParamDto> allParam = SystemParamConst.getAllParam();
        Map<String, List<SystemParamDto>> groupMap = allParam.stream().collect(Collectors.groupingBy(SystemParamDto::getGroupCode));
        return groupMap.entrySet().stream().map(entry -> {
            SystemParamGroupDto systemParamGroupDto = new SystemParamGroupDto();
            systemParamGroupDto.setCode(entry.getKey());
            systemParamGroupDto.setName(entry.getValue().get(0).getGroupName());
            systemParamGroupDto.setChildren(entry.getValue());
            return systemParamGroupDto;
        }).collect(Collectors.toList());
    }

    /**
     * 查询指定技能的详细信息。
     *
     * @param skillGUID 技能的唯一标识符，不能为空。
     * @return SkillRequestDto 技能请求数据传输对象，包含技能的详细信息。如果指定技能不存在，则返回null。
     * @throws IllegalArgumentException 如果skillGUID为null或空字符串，则抛出此异常。
     */
    @Override
    public SkillRequestDto detail(String skillGUID) {
        // 验证skillGUID的有效性
        if (skillGUID == null || skillGUID.isEmpty()) {
            throw new IllegalArgumentException("skillGUID cannot be null or empty");
        }

        // 根据skillGUID查询技能实体
        SkillEntity skillEntity = skillDao.selectOne(new LambdaQueryWrapper<SkillEntity>().eq(SkillEntity::getSkillGUID, skillGUID));
        if (skillEntity == null) {
            return null; // 如果查询不到对应的技能实体，返回null
        }

        if (SKillUpgradeExecutor.isNeedUpgrade(skillEntity.getMetaDataVersion())) {
            // 准备并初始化新的技能实体
            SkillRequestDto skillRequestDto = SkillRequestDto.coverToDto(skillEntity.getMetadata());
            saveAndUpdateNode(skillRequestDto);
            skillEntity = skillDao.selectOne(new LambdaQueryWrapper<SkillEntity>().eq(SkillEntity::getSkillGUID, skillGUID));
        }

        // 将技能实体转换为技能请求DTO，
        SkillRequestDto skillRequestDto = SkillRequestDto.coverToDto(skillEntity.getMetadata());
        skillRequestDto.setId(skillGUID);
        return skillRequestDto;
    }

    /**
     * 根据助理代码进行技能基础查询。
     * 该方法限定最多查询出5个技能实体。
     *
     * @param skillGUIDs 技能GUID。
     * @return 返回一个技能实体列表，最多包含5个实体。
     */
    @Override
    public List<SkillBaseDto> skillBaseQuery(List<String> skillGUIDs, String mode, String spaceGUID, Integer isSystemAssistant, String skillCategoryGUID, String skillName) {
        LambdaQueryWrapper<SkillEntity> wrapper = new LambdaQueryWrapper<>();
        //仅查询允许对话框窗口的技能
        if (StringUtils.isEmpty(mode) || !"debug".equals(mode)) {
            if (!CollectionUtils.isEmpty(skillGUIDs) && skillGUIDs.size() > 0) {
                wrapper.in(SkillEntity::getSkillGUID, skillGUIDs);
            }
            wrapper.eq(SkillEntity::getOpenDialogWindow, 1);
            if (isSystemAssistant != 1) {
                wrapper.eq(SkillEntity::getSpaceGUID, spaceGUID);
            }
            if (!StringUtils.isEmpty(skillCategoryGUID)) {
                wrapper.like(SkillEntity::getSkillCategoryGUIDs, skillCategoryGUID);
            }
            if (!StringUtils.isEmpty(skillName)) {
                wrapper.like(SkillEntity::getSkillName, skillName);
            }
        }
        List<SkillEntity> skillEntities = skillDao.selectList(wrapper);
        //skillEntities.get(0).getMetadata();
        if (CollectionUtils.isEmpty(skillEntities)) {
            return new ArrayList<>();
        }
        //汇总skillEntities的主键
        List<String> skillGUIDResult = skillEntities.stream().map(SkillEntity::getSkillGUID).collect(Collectors.toList());
        //根据技能 ID 查询技能问题表，返回所有技能的问题
        List<SkillQuestionsEntity> skillQuestionsEntities = skillQuestionsDao.selectBySkillGUIDs(skillGUIDResult);
        //List<SkillNodeEntity> skillNodeEntities = skillQuestionsDao.selectDocumentAnalysisNodeBySkillGUIDs(skillGUIDResult);
        List<SkillBaseDto> skillBaseDtos = Lists.newArrayList();
        skillGUIDResult.forEach(c -> {
            SkillEntity skillEntity = skillEntities.stream().filter(c1 -> c1.getSkillGUID().equals(c)).findFirst().orElse(null);
            SkillBaseDto skillBaseDto = BeanUtil.copyProperties(skillEntity, SkillBaseDto.class);
            skillBaseDto.setHasStartup(!StringUtils.isEmpty(skillEntity.getStartUpPluginGUID()) && !StringUtils.isEmpty(skillEntity.getStartUpToolGUID()));
            skillBaseDto.setHasDocumentAnalysisNode(skillEntity.getHasDocumentAnalysisNode() != 0);
            skillBaseDto.setHasImageAnalysisNode(skillEntity.getHasImageAnalysisNode() != 0);
            //查询skillQuestionsEntities对应的记录，保留单个question字段不变，新增questions列表
            List<String> questions = skillQuestionsEntities.stream()
                .filter(c1 -> c1.getSkillGUID().equals(c))
                .sorted(Comparator.comparing(c1 -> c1.getSort() == null ? Integer.MAX_VALUE : c1.getSort()))
                .map(SkillQuestionsEntity::getQuestion)
                .collect(Collectors.toList());
            
            // 设置questions列表
            skillBaseDto.setQuestions(questions);
            
            // 保持单个question字段不变（取第一个问题或保持原逻辑）
            skillQuestionsEntities.stream().filter(c1 -> c1.getSkillGUID().equals(c)).findFirst().ifPresent(c1 -> {
                skillBaseDto.setQuestion(c1.getQuestion());
            });
            skillBaseDtos.add(skillBaseDto);
        });
        return skillBaseDtos;
    }

    @Override
    public List<SkillBaseDto> skillBaseQuery(List<String> skillGUIDs) {
        LambdaQueryWrapper<SkillEntity> wrapper = new LambdaQueryWrapper<>();
        if (CollectionUtils.isEmpty(skillGUIDs)) {
            wrapper.eq(SkillEntity::getOpenDialogWindow, 1);
        } else {
            wrapper.in(SkillEntity::getSkillGUID, skillGUIDs).or(e->e.in(SkillEntity::getSkillCode, skillGUIDs));
        }
        //仅查询允许对话框窗口的技能
        List<SkillEntity> skillEntities = skillDao.selectList(wrapper);
        if (CollectionUtils.isEmpty(skillEntities)) {
            return new ArrayList<>();
        }
        //汇总skillEntities的主键
        List<String> skillGUIDResult = skillEntities.stream().map(SkillEntity::getSkillGUID).collect(Collectors.toList());
        //根据技能 ID 查询技能问题表，返回所有技能的问题
        List<SkillQuestionsEntity> skillQuestionsEntities = skillQuestionsDao.selectBySkillGUIDs(skillGUIDResult);
        List<SkillBaseDto> skillBaseDtos = Lists.newArrayList();
        skillGUIDResult.forEach(c -> {

            SkillEntity skillEntity = skillEntities.stream().filter(c1 -> c1.getSkillGUID().equals(c)).findFirst().orElse(null);

            SkillBaseDto skillBaseDto = BeanUtil.copyProperties(skillEntity, SkillBaseDto.class);
            skillBaseDto.setHasStartup(!StringUtils.isEmpty(skillEntity.getStartUpPluginGUID()) && !StringUtils.isEmpty(skillEntity.getStartUpToolGUID()));
            skillBaseDto.setHasDocumentAnalysisNode(skillEntity.getHasDocumentAnalysisNode() != 0);
            skillBaseDto.setHasImageAnalysisNode(skillEntity.getHasImageAnalysisNode() != 0);
            skillBaseDto.setInteractive(skillEntity.getOpenDialogWindow() == 1);
            //查询skillQuestionsEntities对应的记录，保留单个question字段不变，新增questions列表
            List<String> questions = skillQuestionsEntities.stream()
                .filter(c1 -> c1.getSkillGUID().equals(c))
                .sorted(Comparator.comparing(c1 -> c1.getSort() == null ? Integer.MAX_VALUE : c1.getSort()))
                .map(SkillQuestionsEntity::getQuestion)
                .collect(Collectors.toList());
            
            // 设置questions列表
            skillBaseDto.setQuestions(questions);
            
            // 保持单个question字段不变（取第一个问题或保持原逻辑）
            skillQuestionsEntities.stream().filter(c1 -> c1.getSkillGUID().equals(c)).findFirst().ifPresent(c1 -> {
                skillBaseDto.setQuestion(c1.getQuestion());
            });

            SkillRequestDto requestDto = SkillRequestDto.coverToDto(skillEntity.getMetadata());
            if (SkillModeConst.FLOW.equals(requestDto.getMode()) && !ObjectUtils.isEmpty(requestDto.getFlow())) {
                List<SkillNodeDto> nodes = requestDto.getFlow().getNodes();
                if ("Start".equals(nodes.get(0).getType())) {
                    SystemPluginNodeConfigDto configDto = (SystemPluginNodeConfigDto) new NodeConfigProcessor(nodes.get(0)).parseConfigDto();
                    if ("variables".equals(configDto.getOutputType())) {
                        List<AbstractNodeConfigDto.ParamDto> outputs = configDto.getOutputs();
                        skillBaseDto.setInputs(outputs);
                    }
                    boolean hasUseFileUpload = nodes.get(0).getConfig().containsKey("useFileUpload");
                    boolean hasUseQRCodeUpload = nodes.get(0).getConfig().containsKey("useQRCodeUpload");
                    boolean hasMaxUploadNumber = nodes.get(0).getConfig().containsKey("maxUploadNumber");
                    skillBaseDto.setUploadables(configDto.getUploadables());
                    skillBaseDto.setUseFileUpload(hasUseFileUpload ? configDto.isUseFileUpload() : true);
                    skillBaseDto.setMaxUploadNumber(hasMaxUploadNumber ? configDto.getMaxUploadNumber() : 1);
                    skillBaseDto.setUseQRCodeUpload(hasUseQRCodeUpload ? configDto.isUseQRCodeUpload() : true);
                }
            } else if (SkillModeConst.AGENT.equals(requestDto.getMode())) {
                SkillRequestDto.AgentDTO agent = requestDto.getAgent();
                if (!ObjectUtils.isEmpty(agent)) {
                    skillBaseDto.setInputs(agent.getInputs());
                    skillBaseDto.setUploadables(agent.getUploadables());
                    skillBaseDto.setUseFileUpload(agent.isUseFileUpload());
                    skillBaseDto.setUseQRCodeUpload(agent.isUseQRCodeUpload());
                }
            }
            skillBaseDtos.add(skillBaseDto);
        });
        return skillBaseDtos;
    }

    @Override
    public AssistantSkillBaseResponse skillGuideQuery(String skillGUID) {
        if (StringUtils.isEmpty(skillGUID)) {
            throw new IllegalArgumentException("skillGUID cannot be null or empty");
        }
        LambdaQueryWrapper<SkillEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SkillEntity::getSkillGUID, skillGUID);
        List<SkillEntity> skillEntities = skillDao.selectList(wrapper);
        if (CollectionUtils.isEmpty(skillEntities)) {
            throw new IllegalArgumentException("不存在该技能：" + skillGUID);
        }
        // 问题
        LambdaQueryWrapper<SkillQuestionsEntity> questionWrapper = new LambdaQueryWrapper<>();
        questionWrapper.eq(SkillQuestionsEntity::getSkillGUID, skillGUID);
        List<SkillQuestionsEntity> skillQuestionsEntities = skillQuestionsDao.selectList(questionWrapper);
        // 用户示例
        LambdaQueryWrapper<SkillUserExampleEntity> wrapperUserExample = new LambdaQueryWrapper<>();
        wrapperUserExample.eq(SkillUserExampleEntity::getSkillGUID, skillGUID);
        List<SkillUserExampleEntity> skillUserExampleEntities = skillUserExampleDao.selectList(wrapperUserExample);
        AssistantSkillBaseResponse assistantSkillBaseResponse = new AssistantSkillBaseResponse();
        assistantSkillBaseResponse.setSkillGUID(skillGUID);
        SkillEntity skillEntity = skillEntities.get(0);
        assistantSkillBaseResponse.setSkillName(skillEntity.getSkillName());
        assistantSkillBaseResponse.setDescription(skillEntity.getDescribe());
        if (!StringUtils.isEmpty(skillEntity.getIcon())) {
            String documentGuid = JsonUtil.parseTree(skillEntity.getIcon()).get(0).get("documentGuid").textValue();
            String downloadOuterUrl = getDownloadOuterUrl(documentGuid);
            assistantSkillBaseResponse.setIcon(downloadOuterUrl);
        }
        if (!StringUtils.isEmpty(skillEntity.getGuide())) {
            String replacedHtml = replaceImgSrc(skillEntity.getGuide());
            assistantSkillBaseResponse.setGuide(replacedHtml);
        }
        if (!CollectionUtils.isEmpty(skillQuestionsEntities)) {
            assistantSkillBaseResponse.setQuestions(
                    skillQuestionsEntities.stream()
                            .map(skillQuestionsEntity -> {
                                SkillQuestionsDto question = new SkillQuestionsDto();
                                question.setQuestion(skillQuestionsEntity.getQuestion());
                                question.setSort(skillQuestionsEntity.getSort());
                                return question;
                            }).collect(Collectors.toList())
            );
        }
        if (!CollectionUtils.isEmpty(skillUserExampleEntities)) {
            assistantSkillBaseResponse.setExamples(
                    skillUserExampleEntities.stream()
                            .map(skillUserExampleEntity -> {
                                SkillUserExampleDto exampleDto = new SkillUserExampleDto();
                                exampleDto.setUserQuestion(skillUserExampleEntity.getUserQuestion());
                                exampleDto.setAssistantAnswer(skillUserExampleEntity.getAssistantAnswer());
                                exampleDto.setSort(skillUserExampleEntity.getSort());
                                return exampleDto;
                            }).collect(Collectors.toList())
            );
        }
        return assistantSkillBaseResponse;
    }

    public String replaceImgSrc(String html) {

        // 使用正则表达式匹配<img>标签的src属性
        Pattern pattern = Pattern.compile("<img[^>]+src\\s*=\\s*\"([^\"]*)\"");
        Matcher matcher = pattern.matcher(html);

        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            // 获取src属性的值
            String srcValue = matcher.group(1);
            // 提取fileGuid参数值
            String fileGuid = extractFileGuid(srcValue);
            if (!StringUtils.isEmpty(fileGuid)) {
                String downloadOuterUrl = getDownloadOuterUrl(fileGuid);
                // 替换src属性值为fileGuid
                String replacement = matcher.group().replace(srcValue, downloadOuterUrl);
                // 将替换后的字符串追加到StringBuffer中
                matcher.appendReplacement(sb, replacement);
            }
        }
        // 将最后一个匹配之后的剩余字符串追加到StringBuffer中
        matcher.appendTail(sb);
        // 输出修改后的HTML字符串
        return sb.toString();
    }

    private String extractFileGuid(String srcValue) {
        // 使用正则表达式匹配fileGuid参数
        Pattern guidPattern = Pattern.compile("fileGuid=([^&]*)");
        Matcher guidMatcher = guidPattern.matcher(srcValue);
        if (guidMatcher.find()) {
            // 返回fileGuid参数值
            return guidMatcher.group(1);
        }
        return "";
    }

    /**
     * 获取指定技能GUID的问题列表。
     * 该方法将查询与指定技能GUID相关的问题，并返回一个最多包含3个随机问题的列表。
     * 如果相关问题数量少于3个，则返回所有问题。
     *
     * @param skillGUID 技能的全局唯一标识符，不能为空。
     * @return 返回一个字符串列表，包含随机选择的问题，列表最多包含3个问题。
     * @throws BusinessLogicException 如果输入的skillGUID为空，抛出此异常。
     */
    @Override
    public List<String> questionsList(String skillGUID) {
        // 验证输入参数skillGUID的有效性
        if (StringUtils.isEmpty(skillGUID)) {
            throw new BusinessLogicException("查询技能问题列表skillGUID必填");
        }
        // 构建查询条件，查询与指定skillGUID相关的问题
        LambdaQueryWrapper<SkillQuestionsEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SkillQuestionsEntity::getSkillGUID, skillGUID);
        List<SkillQuestionsEntity> skillQuestionsEntities = skillQuestionsDao.selectList(wrapper);
        // 若查询结果为空，直接返回空列表
        if (CollectionUtils.isEmpty(skillQuestionsEntities)) {
            return Lists.newArrayList();
        }
        // 将问题实体列表转换为问题字符串列表后返回
        return skillQuestionsEntities.stream().map(SkillQuestionsEntity::getQuestion).collect(Collectors.toList());
    }

    @Override
    public List<AbstractNodeConfigDto.ParamDto> ParamMapping(HttpServletRequest request, ParamMappingRequestDto paramMappingRequestDto) {
        if (ObjectUtils.isEmpty(paramMappingRequestDto) || CollectionUtils.isEmpty(paramMappingRequestDto.getSource())
                || CollectionUtils.isEmpty(paramMappingRequestDto.getTarget())) {
            return Lists.newArrayList();
        }
        //添加系统默认参数
        List<ParamMappingRequestDto.ParamDto> allParseParam = SystemParamConst.getAllParseParam();
        if (!CollectionUtils.isEmpty(paramMappingRequestDto.getSource())) {
            allParseParam.addAll(paramMappingRequestDto.getSource());
        }
        paramMappingRequestDto.setSource(allParseParam);

        try {
            ActionResultDTO actionResultDTO = this.callEngine(GPTEngineAddress.PARAM_MAPPING, paramMappingRequestDto, this.getMysoftContext(request.getCookies()));
            System.out.println(JsonUtil.toString(actionResultDTO));
            List<AbstractNodeConfigDto.ParamDto> parseValue = ((ArrayList<AbstractNodeConfigDto.ParamDto>) actionResultDTO.getData());
            return parseValue;
        } catch (Exception ex) {
            log.error("#### 参数对比错误 = {}", ex.getMessage());
        }
        return Lists.newArrayList();
    }

    @Override
    public List<SkillBaseDto> querySkillByGUID(List<String> skillGUIDs) {
        // 使用LambdaQueryWrapper构建查询条件，并设置查询结果上限为5个
        LambdaQueryWrapper<SkillEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SkillEntity::getSkillGUID, skillGUIDs);
        //TODO:缺少通过助手查询授权的技能的逻辑  这里放回的就是全部的技能
        List<SkillEntity> skillEntities = skillDao.selectList(wrapper);
        //复制对象到另一个对象
        List<SkillBaseDto> skillBaseDTOs = BeanUtil.copyToList(skillEntities, SkillBaseDto.class);
        return skillBaseDTOs;
    }

    /**
     * 保存技能验证信息的方法。
     * 对传入的技能请求对象进行一系列验证，确保数据的完整性。
     *
     * @param requestDto 技能请求对象，包含技能的编码、名称、模式等信息。
     *                   如果保存模式为基础保存（0），则不进行额外的验证。
     * @throws BusinessLogicException 如果技能编码、名称或模式为空，则抛出业务逻辑异常。
     */
    private void saveValidate(SkillRequestDto requestDto) {
        // 判断编码是否为空
        if (ObjectUtils.isEmpty(requestDto.getCode())) {
            throw new BusinessLogicException("技能编码不能为空");
        }
        // 判断名称是否为空
        if (ObjectUtils.isEmpty(requestDto.getName())) {
            throw new BusinessLogicException("技能名称不能为空");
        }
        // 判断模式是否为空
        if (ObjectUtils.isEmpty(requestDto.getMode())) {
            throw new BusinessLogicException("技能模式不能为空");
        }
        if (StringUtils.isEmpty(requestDto.getId())) {
            requestDto.setCode(workSpaceService.generatePrefixCode(requestDto.getSpaceGUID(), requestDto.getCode()));
        }
        LambdaQueryWrapper<SkillEntity> wrapper = new LambdaQueryWrapper<SkillEntity>().eq(SkillEntity::getSkillCode, requestDto.getCode());
        if (!StringUtils.isEmpty(requestDto.getId())) {
            wrapper.ne(SkillEntity::getSkillGUID, requestDto.getId());
        }
        SkillEntity skillEntity = skillDao.selectOne(wrapper);
        if (!ObjectUtils.isEmpty(skillEntity)) {
            throw new BusinessLogicException("技能编码重复");
        }
        // 如果是基础保存（0），则不需校验下面的数据，直接返回
        if (!ObjectUtils.isEmpty(requestDto.getSaveMode()) && requestDto.getSaveMode() == 1) {
            return;
        }
        if (SkillModeConst.AGENT.equals(requestDto.getMode())) {
            agentValidate(requestDto);
        } else if (SkillModeConst.FLOW.equals(requestDto.getMode())) {
            // 流程模式进行节点验证
            nodeValidate(requestDto);
        }

    }

    private void agentValidate(SkillRequestDto requestDto) {
        if (ObjectUtils.isEmpty(requestDto.getAgent())) {
            throw new BusinessLogicException("技能agent不能为空");
        }
        SkillRequestDto.AgentDTO agent = requestDto.getAgent();
        if (ObjectUtils.isEmpty(agent.getPrompt())) {
            throw new BusinessLogicException("Agent技能的提示词不能为空");
        }

        //添加插件和知识库是否存在的校验
        if (!CollectionUtils.isEmpty(agent.getTools())) {
            agent.getTools().forEach(c -> {
                if(StringUtils.isNotBlank(c.getPluginGUID())){
                    PluginEntity pluginEntity = pluginDao.selectOne(Wrappers.<PluginEntity>lambdaQuery().select(PluginEntity::getPluginGUID).eq(PluginEntity::getPluginGUID, c.getPluginGUID()));
                    if (pluginEntity == null){
                        throw new BusinessLogicException("插件不存在");
                    }
                }
            });
        }
        if (!CollectionUtils.isEmpty(agent.getKnowledgs())) {
            Set<String> codes = agent.getKnowledgs().stream().map(SkillRequestDto.AgentKnowledgeDto::getCode).collect(Collectors.toSet());

            List<String> list = knowledgeDao.selectList(Wrappers.<KnowledgeEntity>lambdaQuery().select(KnowledgeEntity::getCode).in(KnowledgeEntity::getCode, codes)).
                    stream().map(KnowledgeEntity::getCode).collect(Collectors.toList());
            if (list.size() != codes.size()) {
                throw new BusinessLogicException("知识库不存在");
            }
        }
    }

    /**
     * 对技能请求DTO进行节点验证。
     * 验证编排数据、节点数据和连线数据的完整性以及节点的必填项。
     *
     * @param requestDto 技能请求DTO，包含编排信息、节点信息和连线信息。
     * @throws BusinessLogicException 如果编排数据、节点数据、连线数据或节点的必填项为空，则抛出业务逻辑异常。
     */
    private void nodeValidate(SkillRequestDto requestDto) {

        // 判断编排数据、编排下节点数据、编排下连线数据是否为空
        if (ObjectUtils.isEmpty(requestDto.getFlow())) {
            throw new BusinessLogicException("技能编排数据不能为空");
        }
        if (CollectionUtils.isEmpty(requestDto.getFlow().getNodes())) {
            throw new BusinessLogicException("技能编排节点数据不能为空");
        }
        if (CollectionUtils.isEmpty(requestDto.getFlow().getEdges())) {
            throw new BusinessLogicException("技能编排连线数据不能为空");
        }

        // 节点必填项基础校验
        List<SkillNodeDto> nodes = requestDto.getFlow().getNodes();
        nodes.forEach(node -> {
            nodeConfigValidate(node);
        });

        //todo：开始节点校验 outputType 的类型  确定 output 是否需要传递参数
        // 节点连接线校验、参数校验引用校验（TODO 待实现）
    }

    /**
     * 节点配置校验
     *
     * @param node
     */
    private void nodeConfigValidate(SkillNodeDto node) {
        new NodeConfigProcessor(node).configValidate();
    }

    /**
     * 递增给定的版本号。
     * <p>
     * 此方法接收一个字符串类型的版本号，将其递增后返回。版本号的前缀为"V"，如果存在，将被保留。递增规则为：
     * 1. 移除版本号前的"V"前缀（如果存在）；
     * 2. 将版本号字符串转换为整数；
     * 3. 整数递增；
     * 4. 将递增后的整数格式化为三位数，不足三位时前面补零；
     * 5. 在递增后的数字前添加"V"前缀后返回。
     *
     * @param version 当前的版本号字符串，预期格式为数字，可选前缀为"V"。
     * @return 递增后的版本号字符串，格式为"V000"，其中"000"为递增后的数字，不足三位时前面补零。
     */
    public static String incrementVersion(String version) {
        // 移除版本号前的"V"前缀（如果存在）
        if (version.startsWith("V")) {
            version = version.substring(1);
        }
        // 将版本号字符串转换为整数并递增
        int number = Integer.parseInt(version);
        number++;
        // 将递增后的整数格式化为三位数，不足时前面补零
        String incrementedNumber = String.format("%03d", number);
        // 在递增后的数字前添加"V"前缀后返回
        return "V" + incrementedNumber;
    }

    @Override
    public List<AssistInputMyApplicationDto> getAllMyApplications(){
        return aiAssistInputClient.getAllMyApplications();
    }

    @Override
    public List<AssistInputFunctionControlDto> getFunctionControlsByAppCode(AssistInputFunctionControlParamDto searchFunctionControlDto){
        return aiAssistInputClient.getFunctionControlsByAppCode(searchFunctionControlDto);
    }

    @Override
    public List<AssistInputFunctionControlFieldDto> getFunctionControlFieldsById(UUID id){
        AssistInputFieldParamDto assistInputFieldParamDto = new AssistInputFieldParamDto();
        assistInputFieldParamDto.setId(id.toString());
        return aiAssistInputClient.getFunctionControlFieldsById(assistInputFieldParamDto);
    }

    @Override
    public boolean refreshSkillCollectCount(String skillGUID, int change) {
        SkillEntity skillEntity = skillDao.selectById(skillGUID);
        if (skillEntity != null) {
            skillEntity.setSkillCollectCount(skillEntity.getSkillCollectCount() + change);
            return skillDao.updateById(skillEntity) > 0;
        }
        return false;
    }

    @Override
    public List<PlatformSkillListResultDto> platformSkillList(PlatformSkillListParamsDto dto) {
        WorkSpaceEntity workSpaceEntity = workSpaceDao.selectOne(Wrappers.<WorkSpaceEntity>lambdaQuery().eq(WorkSpaceEntity::getSpaceCode, dto.getSpace()));
        if (workSpaceEntity == null) {
            return new ArrayList<>();
        }
        if ("skill".equals(dto.getType())) {
            LambdaQueryWrapper<SkillEntity> wrapper = Wrappers.<SkillEntity>lambdaQuery().eq(SkillEntity::getSpaceGUID, workSpaceEntity.getSpaceGUID());
            if (StringUtils.isNotEmpty(dto.getKey())) {
                wrapper.and(w -> w.like(SkillEntity::getSkillName, dto.getKey())
                        .or().like(SkillEntity::getSkillCode, dto.getKey()));
            }
            wrapper.last("limit 100");
            return skillDao.selectList(wrapper).stream().map(item -> {
                PlatformSkillListResultDto resultDto = new PlatformSkillListResultDto();
                resultDto.setCode(item.getSkillCode());
                resultDto.setName(item.getSkillName());
                resultDto.setId(item.getSkillGUID());
                return resultDto;
            }).collect(Collectors.toList());
        }else if("plan".equals(dto.getType())) {
            LambdaQueryWrapper<PlanEntity> wrapper = Wrappers.<PlanEntity>lambdaQuery().eq(PlanEntity::getSpaceGUID, workSpaceEntity.getSpaceGUID());
            if (StringUtils.isNotEmpty(dto.getKey())) {
                wrapper.and(w -> w.like(PlanEntity::getPlanCode, dto.getKey())
                        .or().like(PlanEntity::getPlanName, dto.getKey()));
            }
            wrapper.last("limit 100");

            return planDao.selectList(wrapper).stream().map(item -> {
                PlatformSkillListResultDto resultDto = new PlatformSkillListResultDto();
                resultDto.setCode(item.getPlanCode());
                resultDto.setName(item.getPlanName());
                resultDto.setId(item.getPlanGUID());
                return resultDto;
            }).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public List<String> getPlanNodeGUID(String objectGUID) {
        List<SkillNodeEntity> skillNodeEntities = skillNodeDao.selectList(Wrappers.<SkillNodeEntity>lambdaQuery().eq(SkillNodeEntity::getSkillGUID, objectGUID));
        if (CollectionUtils.isEmpty(skillNodeEntities)) {
            return new ArrayList<>();
        }
        //获取guid的集合
        List<String> planGUID = skillNodeEntities.stream().filter(skillNodeEntity -> skillNodeEntity.getNodeType().equals("Plan")).map(SkillNodeEntity::getPlanGUID).collect(Collectors.toList());
        return planGUID;
    }

    @Override
    public String selectPlugin(String pluginGUID) {
        SkillEntity skillEntity = skillDao.selectByPluginGUID(pluginGUID);
        if (skillEntity != null) {
            return skillEntity.getSkillName();
        }
        LambdaQueryWrapper<SkillResourceEntity> wrapper = Wrappers.<SkillResourceEntity>lambdaQuery()
                .eq(SkillResourceEntity::getResourceGUID, pluginGUID)
                .eq(SkillResourceEntity::getResourceType, "Plugin");
        List<SkillResourceEntity> skillResourceEntities = skillResourceDao.selectList(wrapper);
        if (skillResourceEntities == null || skillResourceEntities.isEmpty()) {
            return null;
        }

        Collection<String> skillGUIDs = skillResourceEntities.stream().map(SkillResourceEntity::getSkillGUID).collect(Collectors.toSet());

        List<SkillEntity> skillEntities = skillDao.selectList(Wrappers.<SkillEntity>lambdaQuery().in(SkillEntity::getSkillGUID, skillGUIDs));
        if (skillEntities == null || skillEntities.isEmpty()) {
            // 引用的技能被删除
            return null;
        }
        List<String> skillNames = skillEntities.stream().map(SkillEntity::getSkillName).collect(Collectors.toList());
        return StringUtils.join(skillNames, ",");
    }

    @Override
    public String selectKnowledge(String knowledgeCode) {
        SkillEntity skillEntity = skillDao.selectByKnowledgeCode(knowledgeCode);
        if (skillEntity !=null) {
            return skillEntity.getSkillName();
        }
        LambdaQueryWrapper<SkillResourceEntity> wrapper = Wrappers.<SkillResourceEntity>lambdaQuery()
                .eq(SkillResourceEntity::getResourceCode, knowledgeCode)
                .eq(SkillResourceEntity::getResourceType, "Knowledge");
        List<SkillResourceEntity> skillResourceEntities = skillResourceDao.selectList(wrapper);
        if (skillResourceEntities == null || skillResourceEntities.isEmpty()) {
            return null;
        }

        Collection<String> skillGUIDs = skillResourceEntities.stream().map(SkillResourceEntity::getSkillGUID).collect(Collectors.toSet());

        List<SkillEntity> skillEntities = skillDao.selectList(Wrappers.<SkillEntity>lambdaQuery().in(SkillEntity::getSkillGUID, skillGUIDs));
        if (skillEntities == null || skillEntities.isEmpty()) {
            // 引用的技能被删除
            return null;
        }
        List<String> skillNames = skillEntities.stream().map(SkillEntity::getSkillName).collect(Collectors.toList());
        return StringUtils.join(skillNames, ",");
    }

    /**
     * 根据 MCP 服务 GUID 查询使用该服务的技能名称
     * 用于在删除 MCP 服务前检查是否被技能引用
     *
     * @param mcpServiceGUID MCP 服务的 GUID
     * @return 使用该 MCP 服务的技能名称，如果没有被引用则返回 null
     */
    public String selectMcpService(String mcpServiceGUID) {
        // 查询 gpt_SkillResource 表中 ResourceType 为 "Mcp" 且 ResourceGUID 为指定值的记录
        SkillResourceEntity skillResourceEntity = skillResourceDao.selectOne(
            Wrappers.<SkillResourceEntity>lambdaQuery()
                .eq(SkillResourceEntity::getResourceGUID, mcpServiceGUID)
                .eq(SkillResourceEntity::getResourceType, "Mcp"));
        if (ObjectUtils.isEmpty(skillResourceEntity)) {
            return null;
        }
        SkillEntity entity = skillDao.selectById(skillResourceEntity.getSkillGUID());
        if (entity == null) {
            return null;
        }
        return entity.getSkillName();
    }

    /**
     * 根据 MCP 工具 GUID 查询使用该工具的技能名称
     * 用于在删除 MCP 工具前检查是否被技能引用
     *
     * @param mcpToolGUID MCP 工具的 GUID
     * @return 使用该 MCP 工具的技能名称，如果没有被引用则返回 null
     */
    public String selectMcpTool(String mcpToolGUID) {
        // 查询 gpt_SkillResource 表中 ResourceType 为 "McpTool" 且 ResourceGUID 为指定值的记录
        SkillResourceEntity skillResourceEntity = skillResourceDao.selectOne(
            Wrappers.<SkillResourceEntity>lambdaQuery()
                .eq(SkillResourceEntity::getResourceGUID, mcpToolGUID)
                .eq(SkillResourceEntity::getResourceType, "McpTool"));
        if (ObjectUtils.isEmpty(skillResourceEntity)) {
            return null;
        }
        SkillEntity entity = skillDao.selectById(skillResourceEntity.getSkillGUID());
        if (entity == null) {
            return null;
        }
        return entity.getSkillName();
    }
}
