package com.mysoft.gptbuilder.agent.service.service;

import com.mysoft.gptbuilder.agent.model.dto.skill.SkillRequestDto;
import com.mysoft.gptbuilder.agent.service.service.SkillService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SkillService测试类，用于测试Agent模式下的默认modelInstanceCode设置
 */
@SpringBootTest
@SpringJUnitConfig
public class SkillServiceTest {

    @Test
    public void testAgentWithEmptyModelInstanceCode() {
        // 创建测试数据，模拟前端提交的JSON数据
        SkillRequestDto requestDto = new SkillRequestDto();
        requestDto.setId("b16a379f-dc6e-412f-9a63-a1648ff2e95e");
        requestDto.setCode("tj_dap_test_agent");
        requestDto.setName("测试用智能体");
        requestDto.setMode("agent");
        requestDto.setSpaceGUID("3a1b4825-7afb-50a3-0ee1-30ee1051d18a");
        
        // 创建Agent对象，modelInstanceCode为空
        SkillRequestDto.AgentDTO agent = new SkillRequestDto.AgentDTO();
        agent.setModelInstanceCode(""); // 空字符串
        
        // 创建prompt对象
        SkillRequestDto.PromptDto prompt = new SkillRequestDto.PromptDto();
        prompt.setTemplate("你是一个天才，你会鼓励我");
        agent.setPrompt(prompt);
        
        // 创建executionSetting对象，设置为空字符串来测试反序列化
        agent.setExecutionSetting(null); // 这里设置为null，模拟空字符串反序列化后的结果
        
        requestDto.setAgent(agent);
        
        // 测试JSON反序列化不会报错
        assertDoesNotThrow(() -> {
            // 这里模拟SkillService中的逻辑
            if (agent.getModelInstanceCode() == null || agent.getModelInstanceCode().isEmpty()) {
                agent.setModelInstanceCode("default_text_generation");
            }
        });
        
        // 验证默认值设置成功
        assertEquals("default_text_generation", agent.getModelInstanceCode());
    }
    
    @Test
    public void testExecutionSettingDeserializationWithEmptyString() {
        // 测试executionSetting字段的空字符串反序列化
        SkillRequestDto.AgentDTO agent = new SkillRequestDto.AgentDTO();
        
        // 模拟空字符串反序列化为null的情况
        agent.setExecutionSetting(null);
        
        // 验证不会抛出异常
        assertDoesNotThrow(() -> {
            SkillRequestDto.executionSettingDto setting = agent.getExecutionSetting();
            // 空字符串应该被反序列化为null
            assertNull(setting);
        });
    }
}
