# MCP工具删除检查功能实现总结

## 实现的功能

参考Knowledges的删除检查逻辑，为MCP工具实现了类似的删除检查功能。当使用`skill/saveAndUpdateNode`保存技能时，会检查技能中引用的MCP工具是否被删除，如果被删除会提示用户。

## 修改的文件

### 1. SkillService.java

#### 添加的import语句
```java
import com.mysoft.gptbuilder.agent.service.dao.entity.McpServiceEntity;
import com.mysoft.gptbuilder.agent.service.dao.entity.McpServiceToolEntity;
```

#### 添加的DAO注入
```java
@Autowired
private McpServiceToolDao mcpServiceToolDao;

@Autowired
private McpManagerDao mcpManagerDao;
```

#### 修改的方法：saveAgentResource
在处理MCP资源之前添加了验证逻辑：
```java
if (!CollectionUtils.isEmpty(agent.getMcps())) {
    // 验证MCP工具和服务是否存在
    validateMcpResources(agent.getMcps());
    
    agent.getMcps().forEach(mcp -> {
        // 原有的保存逻辑...
    });
}
```

#### 新增的方法：validateMcpResources
```java
private void validateMcpResources(List<SkillRequestDto.AgentMcpDto> mcps) {
    List<String> missingServices = new ArrayList<>();
    List<String> missingTools = new ArrayList<>();

    for (SkillRequestDto.AgentMcpDto mcp : mcps) {
        // 检查MCP工具是否存在
        if (StringUtils.isNotEmpty(mcp.getToolGUID()) && StringUtils.isNotEmpty(mcp.getToolName())) {
            // 查询MCP工具是否存在且状态为启用
            McpServiceToolEntity toolEntity = mcpServiceToolDao.selectOne(
                Wrappers.<McpServiceToolEntity>lambdaQuery()
                    .eq(McpServiceToolEntity::getToolGUID, mcp.getToolGUID())
                    .eq(McpServiceToolEntity::getStatus, 1));
            
            if (ObjectUtils.isEmpty(toolEntity)) {
                missingTools.add(mcp.getToolName());
            }
        }
        // 检查MCP服务是否存在
        else if (StringUtils.isNotEmpty(mcp.getServiceGUID())) {
            // 查询MCP服务是否存在且状态为启用
            McpServiceEntity serviceEntity = mcpManagerDao.selectOne(
                Wrappers.<McpServiceEntity>lambdaQuery()
                    .eq(McpServiceEntity::getServiceGUID, mcp.getServiceGUID())
                    .eq(McpServiceEntity::getStatus, 1));
            
            if (ObjectUtils.isEmpty(serviceEntity)) {
                missingServices.add(StringUtils.isNotEmpty(mcp.getServiceCode()) ? mcp.getServiceCode() : mcp.getServiceGUID());
            }
        }
    }

    // 如果有缺失的资源，抛出异常
    List<String> errorMessages = new ArrayList<>();
    if (!CollectionUtils.isEmpty(missingServices)) {
        errorMessages.add("MCP服务不存在或已被删除: " + String.join(",", missingServices));
    }
    if (!CollectionUtils.isEmpty(missingTools)) {
        errorMessages.add("MCP工具不存在或已被删除: " + String.join(",", missingTools));
    }
    
    if (!errorMessages.isEmpty()) {
        throw new BusinessLogicException(String.join("; ", errorMessages));
    }
}
```

## 功能说明

### 检查逻辑
1. **MCP工具检查**：当AgentMcpDto中包含toolGUID和toolName时，检查gpt_McpServiceTool表中是否存在对应的工具且状态为启用(status=1)
2. **MCP服务检查**：当AgentMcpDto中只包含serviceGUID时，检查gpt_McpService表中是否存在对应的服务且状态为启用(status=1)

### 错误提示
- 如果有MCP服务不存在：`MCP服务不存在或已被删除: [服务编码列表]`
- 如果有MCP工具不存在：`MCP工具不存在或已被删除: [工具名称列表]`
- 如果同时有服务和工具不存在，会用分号分隔两个错误信息

### 与Knowledges检查的对比
- **相似点**：都是在保存前进行验证，都会抛出BusinessLogicException
- **不同点**：
  - Knowledges检查的是知识库编码(code)
  - MCP检查的是工具GUID和服务GUID，同时检查状态字段
  - MCP需要区分工具和服务两种类型的资源

## 测试建议

1. **正常情况测试**：保存包含有效MCP工具和服务的技能
2. **工具删除测试**：删除MCP工具后尝试保存引用该工具的技能
3. **服务删除测试**：删除MCP服务后尝试保存引用该服务的技能
4. **混合删除测试**：同时删除工具和服务后保存技能
5. **状态禁用测试**：将MCP工具或服务状态设为0后保存技能

## 注意事项

1. 该功能只在Agent模式的技能保存时生效
2. 检查的是数据库中的实际存在性和状态，不是引擎连接性
3. 错误信息会明确指出哪些具体的工具或服务不存在
4. 保持了与现有Knowledges检查逻辑的一致性
